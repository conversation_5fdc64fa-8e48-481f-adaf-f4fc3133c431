<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:bioc="http://bpmn.io/schema/bpmn/biocolor/1.0" xmlns:flowable="http://flowable.org/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.flowable.org/processdef">
  <process id="leave1" name="请假流程（普通流程）">
    <startEvent id="startNode1" name="开始">
      <outgoing>Flow_1f4xioj</outgoing>
    </startEvent>
    <userTask id="Activity_14633hx" name="申请人" flowable:formKey="static:1">
      <incoming>Flow_1f4xioj</incoming>
      <outgoing>Flow_0cy98fl</outgoing>
    </userTask>
    <sequenceFlow id="Flow_1f4xioj" sourceRef="startNode1" targetRef="Activity_14633hx" />
    <userTask id="Activity_0lym9dc" name="组长" flowable:candidateUsers="1,3">
      <extensionElements />
      <incoming>Flow_0cy98fl</incoming>
      <outgoing>Flow_1o16t5v</outgoing>
    </userTask>
    <sequenceFlow id="Flow_0cy98fl" sourceRef="Activity_14633hx" targetRef="Activity_0lym9dc" />
    <userTask id="Activity_1j25s1c" name="部门领导" flowable:assignee="1">
      <extensionElements />
      <incoming>Flow_1o16t5v</incoming>
      <outgoing>Flow_0s1t2f2</outgoing>
    </userTask>
    <sequenceFlow id="Flow_1o16t5v" sourceRef="Activity_0lym9dc" targetRef="Activity_1j25s1c" />
    <endEvent id="Event_1jib7oq">
      <incoming>Flow_0s1t2f2</incoming>
    </endEvent>
    <sequenceFlow id="Flow_0s1t2f2" sourceRef="Activity_1j25s1c" targetRef="Event_1jib7oq" />
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_flow">
    <bpmndi:BPMNPlane id="BPMNPlane_flow" bpmnElement="leave1">
      <bpmndi:BPMNShape id="BPMNShape_startNode1" bpmnElement="startNode1" bioc:stroke="">
        <omgdc:Bounds x="240" y="200" width="30" height="30" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="242" y="237" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_14633hx_di" bpmnElement="Activity_14633hx">
        <omgdc:Bounds x="320" y="175" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0lym9dc_di" bpmnElement="Activity_0lym9dc">
        <omgdc:Bounds x="470" y="175" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1j25s1c_di" bpmnElement="Activity_1j25s1c">
        <omgdc:Bounds x="620" y="175" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1jib7oq_di" bpmnElement="Event_1jib7oq">
        <omgdc:Bounds x="772" y="197" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1f4xioj_di" bpmnElement="Flow_1f4xioj">
        <di:waypoint x="270" y="215" />
        <di:waypoint x="320" y="215" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0cy98fl_di" bpmnElement="Flow_0cy98fl">
        <di:waypoint x="420" y="215" />
        <di:waypoint x="470" y="215" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o16t5v_di" bpmnElement="Flow_1o16t5v">
        <di:waypoint x="570" y="215" />
        <di:waypoint x="620" y="215" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0s1t2f2_di" bpmnElement="Flow_0s1t2f2">
        <di:waypoint x="720" y="215" />
        <di:waypoint x="772" y="215" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
