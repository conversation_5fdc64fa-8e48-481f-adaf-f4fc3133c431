<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:bioc="http://bpmn.io/schema/bpmn/biocolor/1.0" xmlns:flowable="http://flowable.org/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.flowable.org/processdef">
  <process id="leave2" name="请假流程（排他网关）">
    <startEvent id="startNode1" name="开始">
      <outgoing>Flow_0q78air</outgoing>
    </startEvent>
    <userTask id="Activity_19b1i4j" name="申请人" flowable:formKey="static:1">
      <incoming>Flow_0q78air</incoming>
      <outgoing>Flow_129vtbe</outgoing>
    </userTask>
    <sequenceFlow id="Flow_0q78air" sourceRef="startNode1" targetRef="Activity_19b1i4j" />
    <userTask id="Activity_0r8rs5v" name="组长" default="Flow_1z12r58" flowable:assignee="1">
      <extensionElements />
      <incoming>Flow_129vtbe</incoming>
      <outgoing>Flow_1z12r58</outgoing>
      <outgoing>Flow_0bt4srq</outgoing>
    </userTask>
    <sequenceFlow id="Flow_129vtbe" sourceRef="Activity_19b1i4j" targetRef="Activity_0r8rs5v" />
    <userTask id="Activity_0iw78d3" name="部门领导" flowable:candidateGroups="1,2,3,4">
      <incoming>Flow_1z12r58</incoming>
      <outgoing>Flow_0nj4k00</outgoing>
    </userTask>
    <userTask id="Activity_1ex621m" name="总经理" flowable:assignee="1">
      <extensionElements />
      <incoming>Flow_0bt4srq</incoming>
      <outgoing>Flow_0fo3v6j</outgoing>
    </userTask>
    <endEvent id="Event_1shlpsv">
      <incoming>Flow_0nj4k00</incoming>
      <incoming>Flow_0fo3v6j</incoming>
    </endEvent>
    <sequenceFlow id="Flow_0nj4k00" sourceRef="Activity_0iw78d3" targetRef="Event_1shlpsv" />
    <sequenceFlow id="Flow_0fo3v6j" sourceRef="Activity_1ex621m" targetRef="Event_1shlpsv" />
    <sequenceFlow id="Flow_1z12r58" sourceRef="Activity_0r8rs5v" targetRef="Activity_0iw78d3" />
    <sequenceFlow id="Flow_0bt4srq" sourceRef="Activity_0r8rs5v" targetRef="Activity_1ex621m">
      <conditionExpression xsi:type="tFormalExpression">${entity.leaveDays &gt; 2}</conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_flow">
    <bpmndi:BPMNPlane id="BPMNPlane_flow" bpmnElement="leave2">
      <bpmndi:BPMNShape id="BPMNShape_startNode1" bpmnElement="startNode1" bioc:stroke="">
        <omgdc:Bounds x="240" y="200" width="30" height="30" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="242" y="237" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_19b1i4j_di" bpmnElement="Activity_19b1i4j">
        <omgdc:Bounds x="320" y="175" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0r8rs5v_di" bpmnElement="Activity_0r8rs5v">
        <omgdc:Bounds x="470" y="175" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0iw78d3_di" bpmnElement="Activity_0iw78d3">
        <omgdc:Bounds x="640" y="100" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ex621m_di" bpmnElement="Activity_1ex621m">
        <omgdc:Bounds x="640" y="250" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1shlpsv_di" bpmnElement="Event_1shlpsv">
        <omgdc:Bounds x="802" y="197" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0q78air_di" bpmnElement="Flow_0q78air">
        <di:waypoint x="270" y="215" />
        <di:waypoint x="320" y="215" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_129vtbe_di" bpmnElement="Flow_129vtbe">
        <di:waypoint x="420" y="215" />
        <di:waypoint x="470" y="215" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0nj4k00_di" bpmnElement="Flow_0nj4k00">
        <di:waypoint x="740" y="140" />
        <di:waypoint x="771" y="140" />
        <di:waypoint x="771" y="215" />
        <di:waypoint x="802" y="215" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fo3v6j_di" bpmnElement="Flow_0fo3v6j">
        <di:waypoint x="740" y="290" />
        <di:waypoint x="771" y="290" />
        <di:waypoint x="771" y="215" />
        <di:waypoint x="802" y="215" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1z12r58_di" bpmnElement="Flow_1z12r58">
        <di:waypoint x="570" y="215" />
        <di:waypoint x="605" y="215" />
        <di:waypoint x="605" y="140" />
        <di:waypoint x="640" y="140" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bt4srq_di" bpmnElement="Flow_0bt4srq">
        <di:waypoint x="570" y="215" />
        <di:waypoint x="605" y="215" />
        <di:waypoint x="605" y="290" />
        <di:waypoint x="640" y="290" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
