<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:bioc="http://bpmn.io/schema/bpmn/biocolor/1.0" xmlns:flowable="http://flowable.org/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.flowable.org/processdef">
  <process id="leave4" name="请假流程（包容网关）">
    <startEvent id="startNode1" name="开始">
      <outgoing>Flow_14qet78</outgoing>
    </startEvent>
    <userTask id="Activity_0uscrk3" name="申请人" flowable:formKey="static:1">
      <incoming>Flow_14qet78</incoming>
      <outgoing>Flow_171lpw3</outgoing>
    </userTask>
    <sequenceFlow id="Flow_14qet78" sourceRef="startNode1" targetRef="Activity_0uscrk3" />
    <userTask id="Activity_0ped7fd" name="科研部门" flowable:candidateUsers="1,3">
      <extensionElements />
      <incoming>Flow_16qxdzv</incoming>
      <outgoing>Flow_01rdmuq</outgoing>
    </userTask>
    <userTask id="Activity_1e8dxc6" name="总经理" flowable:candidateUsers="1">
      <extensionElements />
      <incoming>Flow_0rt1gbx</incoming>
      <outgoing>Flow_01maojf</outgoing>
    </userTask>
    <userTask id="Activity_0xun74h" name="综合部门" flowable:assignee="1">
      <extensionElements />
      <incoming>Flow_15anuo0</incoming>
      <outgoing>Flow_1j0t4se</outgoing>
    </userTask>
    <sequenceFlow id="Flow_171lpw3" sourceRef="Activity_0uscrk3" targetRef="Gateway_0qj0eur" />
    <sequenceFlow id="Flow_16qxdzv" sourceRef="Gateway_0qj0eur" targetRef="Activity_0ped7fd" />
    <sequenceFlow id="Flow_15anuo0" sourceRef="Gateway_0qj0eur" targetRef="Activity_0xun74h">
      <conditionExpression xsi:type="tFormalExpression">${entity.leaveDays &gt; 2}</conditionExpression>
    </sequenceFlow>
    <endEvent id="Event_0k2b5e5">
      <incoming>Flow_01maojf</incoming>
    </endEvent>
    <sequenceFlow id="Flow_01maojf" sourceRef="Activity_1e8dxc6" targetRef="Event_0k2b5e5" />
    <sequenceFlow id="Flow_01rdmuq" sourceRef="Activity_0ped7fd" targetRef="Gateway_05y03rn" />
    <sequenceFlow id="Flow_1j0t4se" sourceRef="Activity_0xun74h" targetRef="Gateway_05y03rn" />
    <sequenceFlow id="Flow_0rt1gbx" sourceRef="Gateway_05y03rn" targetRef="Activity_1e8dxc6" />
    <inclusiveGateway id="Gateway_0qj0eur">
      <incoming>Flow_171lpw3</incoming>
      <outgoing>Flow_16qxdzv</outgoing>
      <outgoing>Flow_15anuo0</outgoing>
    </inclusiveGateway>
    <inclusiveGateway id="Gateway_05y03rn">
      <incoming>Flow_01rdmuq</incoming>
      <incoming>Flow_1j0t4se</incoming>
      <outgoing>Flow_0rt1gbx</outgoing>
    </inclusiveGateway>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_flow">
    <bpmndi:BPMNPlane id="BPMNPlane_flow" bpmnElement="leave4">
      <bpmndi:BPMNShape id="BPMNShape_startNode1" bpmnElement="startNode1" bioc:stroke="">
        <omgdc:Bounds x="235" y="205" width="30" height="30" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="238" y="242" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0uscrk3_di" bpmnElement="Activity_0uscrk3">
        <omgdc:Bounds x="320" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ped7fd_di" bpmnElement="Activity_0ped7fd">
        <omgdc:Bounds x="590" y="120" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1e8dxc6_di" bpmnElement="Activity_1e8dxc6">
        <omgdc:Bounds x="850" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0xun74h_di" bpmnElement="Activity_0xun74h">
        <omgdc:Bounds x="590" y="240" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0k2b5e5_di" bpmnElement="Event_0k2b5e5">
        <omgdc:Bounds x="1022" y="202" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_041zo9p_di" bpmnElement="Gateway_0qj0eur">
        <omgdc:Bounds x="475" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1cyx391_di" bpmnElement="Gateway_05y03rn">
        <omgdc:Bounds x="745" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_14qet78_di" bpmnElement="Flow_14qet78">
        <di:waypoint x="265" y="220" />
        <di:waypoint x="320" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_171lpw3_di" bpmnElement="Flow_171lpw3">
        <di:waypoint x="420" y="220" />
        <di:waypoint x="475" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16qxdzv_di" bpmnElement="Flow_16qxdzv">
        <di:waypoint x="500" y="195" />
        <di:waypoint x="500" y="160" />
        <di:waypoint x="590" y="160" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15anuo0_di" bpmnElement="Flow_15anuo0">
        <di:waypoint x="500" y="245" />
        <di:waypoint x="500" y="280" />
        <di:waypoint x="590" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01maojf_di" bpmnElement="Flow_01maojf">
        <di:waypoint x="950" y="220" />
        <di:waypoint x="1022" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01rdmuq_di" bpmnElement="Flow_01rdmuq">
        <di:waypoint x="690" y="160" />
        <di:waypoint x="770" y="160" />
        <di:waypoint x="770" y="195" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1j0t4se_di" bpmnElement="Flow_1j0t4se">
        <di:waypoint x="690" y="280" />
        <di:waypoint x="770" y="280" />
        <di:waypoint x="770" y="245" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rt1gbx_di" bpmnElement="Flow_0rt1gbx">
        <di:waypoint x="795" y="220" />
        <di:waypoint x="850" y="220" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
