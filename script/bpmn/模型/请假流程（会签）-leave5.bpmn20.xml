<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:bioc="http://bpmn.io/schema/bpmn/biocolor/1.0" xmlns:flowable="http://flowable.org/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.flowable.org/processdef">
  <process id="leave5" name="请假流程（会签）">
    <startEvent id="startNode1" name="开始">
      <outgoing>Flow_1a01nzj</outgoing>
    </startEvent>
    <userTask id="Activity_0x6b71j" name="申请人" flowable:formKey="static:1">
      <incoming>Flow_1a01nzj</incoming>
      <outgoing>Flow_1wdq20a</outgoing>
    </userTask>
    <sequenceFlow id="Flow_1a01nzj" sourceRef="startNode1" targetRef="Activity_0x6b71j" />
    <userTask id="Activity_0dvsmdc" name="串行会签" flowable:assignee="${user}">
      <incoming>Flow_1wdq20a</incoming>
      <outgoing>Flow_1gbyvno</outgoing>
      <multiInstanceLoopCharacteristics isSequential="true" flowable:collection="userList" flowable:elementVariable="user" />
    </userTask>
    <sequenceFlow id="Flow_1wdq20a" sourceRef="Activity_0x6b71j" targetRef="Activity_0dvsmdc" />
    <userTask id="Activity_194idnn" name="并行会签" flowable:assignee="${user}">
      <incoming>Flow_1gbyvno</incoming>
      <outgoing>Flow_06trc15</outgoing>
      <multiInstanceLoopCharacteristics flowable:collection="userList2" flowable:elementVariable="user" />
    </userTask>
    <sequenceFlow id="Flow_1gbyvno" sourceRef="Activity_0dvsmdc" targetRef="Activity_194idnn" />
    <userTask id="Activity_1o4clkg" name="总经理" flowable:assignee="1">
      <extensionElements />
      <incoming>Flow_06trc15</incoming>
      <outgoing>Flow_1jhko1g</outgoing>
    </userTask>
    <sequenceFlow id="Flow_06trc15" sourceRef="Activity_194idnn" targetRef="Activity_1o4clkg" />
    <endEvent id="Event_1it04q9">
      <incoming>Flow_1jhko1g</incoming>
    </endEvent>
    <sequenceFlow id="Flow_1jhko1g" sourceRef="Activity_1o4clkg" targetRef="Event_1it04q9" />
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_flow">
    <bpmndi:BPMNPlane id="BPMNPlane_flow" bpmnElement="leave5">
      <bpmndi:BPMNShape id="BPMNShape_startNode1" bpmnElement="startNode1" bioc:stroke="">
        <omgdc:Bounds x="240" y="200" width="30" height="30" />
        <bpmndi:BPMNLabel>
          <omgdc:Bounds x="242" y="237" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0x6b71j_di" bpmnElement="Activity_0x6b71j">
        <omgdc:Bounds x="320" y="175" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0dvsmdc_di" bpmnElement="Activity_0dvsmdc">
        <omgdc:Bounds x="470" y="175" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_194idnn_di" bpmnElement="Activity_194idnn">
        <omgdc:Bounds x="620" y="175" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1o4clkg_di" bpmnElement="Activity_1o4clkg">
        <omgdc:Bounds x="770" y="175" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1it04q9_di" bpmnElement="Event_1it04q9">
        <omgdc:Bounds x="922" y="197" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1a01nzj_di" bpmnElement="Flow_1a01nzj">
        <di:waypoint x="270" y="215" />
        <di:waypoint x="320" y="215" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wdq20a_di" bpmnElement="Flow_1wdq20a">
        <di:waypoint x="420" y="215" />
        <di:waypoint x="470" y="215" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gbyvno_di" bpmnElement="Flow_1gbyvno">
        <di:waypoint x="570" y="215" />
        <di:waypoint x="620" y="215" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06trc15_di" bpmnElement="Flow_06trc15">
        <di:waypoint x="720" y="215" />
        <di:waypoint x="770" y="215" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jhko1g_di" bpmnElement="Flow_1jhko1g">
        <di:waypoint x="870" y="215" />
        <di:waypoint x="922" y="215" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
