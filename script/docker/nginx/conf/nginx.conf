user  root;
worker_processes  1;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    autoindex on;
    keepalive_timeout  65;
    client_max_body_size 100m;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    map $http_upgrade $connection_upgrade {
        default upgrade;
        ''      close;
    }

    upstream server {
        ip_hash;
        server **********:8081;
    }

    upstream monitor-admin {
        server 127.0.0.1:9090;
    }

    upstream snailjob-server {
        server 127.0.0.1:8800;
    }

    server {
        listen       80;
        server_name  pro.cythcpa.com;

        # Redirect HTTP to HTTPS
        #return 301 https://$host$request_uri;
        rewrite ^(.*) https://$server_name$1 permanent;
    }

    server {
        listen       443 ssl;
        server_name  pro.cythcpa.com;

        # SSL证书路径
        ssl_certificate cert/pro.cythcpa.com.pem;
        ssl_certificate_key cert/pro.cythcpa.com.key;
        #ssl_session_cache shared:SSL:1m;
        ssl_session_timeout 5m;

        # SSL协议和加密套件
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384';
        ssl_prefer_server_ciphers on;

        # HSTS
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

        # 防止点击劫持
        add_header X-Frame-Options DENY;

        # 防止MIME类型嗅探
        add_header X-Content-Type-Options nosniff;

        # 防止XSS攻击
        add_header X-XSS-Protection "1; mode=block";

        # 限制外网访问内网 actuator 相关路径
        location ~ ^(/[^/]*)?/actuator.*(/.*)?$ {
            return 403;
        }



        location / {
            proxy_pass http://127.0.0.1:9000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            proxy_connect_timeout 300;
            chunked_transfer_encoding off;
        }

        location /wpm_main {
            alias  /etc/nginx/html/wpm_main/;
            #alias /usr/local/docker/nginx/html;  # 使用 alias 指向静态文件目录
            try_files $uri $uri/ /wpm_main/index.html;
            index index.html index.htm;
            #autoindex on;
            # 确保去除路径前缀后传递给静态文件目录
            #rewrite ^/wpm/(.*)$ /$1 break;
        }

# 处理 MinIO 桶 wpm 的请求
    location ^~ /wpm/ {
        proxy_pass http://127.0.0.1:9000;  # 确保这个地址是 MinIO 的实际地址
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        proxy_buffering off;

        # 保持路径不变，直接转发给 MinIO
        # 不需要 rewrite，因为 MinIO 需要完整的路径包括 /wpm/
    }

        location /prod-api/ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection $connection_upgrade;
            proxy_pass http://server/;
        }

        location /admin/ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://monitor-admin/admin/;
        }

        location /snail-job/ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://snailjob-server/snail-job/;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }

        # 缓存静态资源
        location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
            expires 30d;
            add_header Cache-Control "public, no-transform";
        }

        # 开启Gzip压缩
        gzip on;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    }
}
