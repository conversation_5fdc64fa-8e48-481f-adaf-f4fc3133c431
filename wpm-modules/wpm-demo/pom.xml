<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.cyth</groupId>
        <artifactId>wpm-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>wpm-demo</artifactId>

    <description>
        demo模块
    </description>

    <dependencies>

        <!-- 通用工具-->
        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-sms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-mail</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-idempotent</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-ratelimiter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-translation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-sensitive</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-encrypt</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-websocket</artifactId>
        </dependency>

    </dependencies>

</project>
