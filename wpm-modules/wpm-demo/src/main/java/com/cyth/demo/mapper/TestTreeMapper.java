package com.cyth.demo.mapper;

import com.cyth.common.mybatis.annotation.DataColumn;
import com.cyth.common.mybatis.annotation.DataPermission;
import com.cyth.common.mybatis.core.mapper.BaseMapperPlus;
import com.cyth.demo.domain.TestTree;
import com.cyth.demo.domain.vo.TestTreeVo;

/**
 * 测试树表Mapper接口
 *
 * <AUTHOR>
 * @date 2021-07-26
 */
@DataPermission({
    @DataColumn(key = "deptName", value = "dept_id"),
    @DataColumn(key = "userName", value = "user_id")
})
public interface TestTreeMapper extends BaseMapperPlus<TestTree, TestTreeVo> {

}
