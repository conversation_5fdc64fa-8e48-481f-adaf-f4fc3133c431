package com.cyth.system.service.captcha;



import cloud.tianai.captcha.common.response.ApiResponse;


import cloud.tianai.captcha.spring.vo.CaptchaResponse;
import cloud.tianai.captcha.spring.vo.ImageCaptchaVO;
import com.cyth.common.core.domain.model.CaptchaCheckBody;

/**
 * <AUTHOR>
 * @version 版本号
 * @date 2024年09月30日 10:07
 * @description
 */
public interface ICaptchaService {
    //生成滑块验证码
    CaptchaResponse<ImageCaptchaVO> getImageCaptchaSlider();

    //验证滑块验证码
    ApiResponse validationImageCaptchaSlider(CaptchaCheckBody captchaCheckBody);
}
