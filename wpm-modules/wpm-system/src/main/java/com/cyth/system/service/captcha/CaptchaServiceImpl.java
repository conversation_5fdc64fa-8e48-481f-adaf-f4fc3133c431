package com.cyth.system.service.captcha;



import cloud.tianai.captcha.common.constant.CaptchaTypeConstant;
import cloud.tianai.captcha.common.response.ApiResponse;


import cloud.tianai.captcha.spring.application.ImageCaptchaApplication;
import cloud.tianai.captcha.spring.vo.CaptchaResponse;
import cloud.tianai.captcha.spring.vo.ImageCaptchaVO;
import com.cyth.common.core.domain.model.CaptchaCheckBody;
import com.cyth.common.core.exception.ServiceException;
import com.cyth.system.exception.SystemExceptionEnums;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;

/**
 * <AUTHOR>
 * @version 版本号
 * @date 2024年09月30日 10:17
 * @description
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CaptchaServiceImpl implements ICaptchaService{
    private final ImageCaptchaApplication captchaApplication;
    @Override
    public CaptchaResponse<ImageCaptchaVO> getImageCaptchaSlider() {
        return  captchaApplication.generateCaptcha(CaptchaTypeConstant.SLIDER);
    }

    @Override
    public ApiResponse validationImageCaptchaSlider(CaptchaCheckBody captchaCheckBody) {
        ApiResponse<?> matching = captchaApplication.matching(captchaCheckBody.getId(), captchaCheckBody.getData());
        if(!matching.isSuccess()){
            //验证码错误
            log.error("滑块验证码校验失败,验证码输入错误,captchaCheckBody:{}",captchaCheckBody);
            throw new ServiceException(SystemExceptionEnums.AUTH_LOGIN_CAPTCHA_CODE_ERROR);
        }else{
            return ApiResponse.ofSuccess(Collections.singletonMap("id", captchaCheckBody.getId()));
        }
    }
}
