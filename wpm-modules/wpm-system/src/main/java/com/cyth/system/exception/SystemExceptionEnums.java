package com.cyth.system.exception;

import com.cyth.common.core.exception.base.IResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 版本号
 * @date 2024年09月30日 10:24
 * @description
 */
@Getter
@AllArgsConstructor
public enum SystemExceptionEnums implements IResponse {
    AUTH_LOGIN_CAPTCHA_CODE_ERROR(1001, "验证码错误"),
    ;
    private final int code;
    private final String msg;

    @Override
    public Integer getCode() {
        return this.code;
    }
    @Override
    public String getMessage() {
        return this.msg;
    }
}
