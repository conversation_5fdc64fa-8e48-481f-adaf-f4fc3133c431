<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.cyth</groupId>
        <artifactId>wpm-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>wpm-system</artifactId>

    <description>
        system系统模块
    </description>

    <dependencies>
        <!-- 通用工具-->
        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-translation</artifactId>
        </dependency>

        <!-- OSS功能模块 -->
        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-log</artifactId>
        </dependency>

        <!-- excel-->
        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-excel</artifactId>
        </dependency>

        <!-- SMS功能模块 -->
        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-sms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-idempotent</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-sensitive</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-encrypt</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-websocket</artifactId>
        </dependency>

        <!-- maven 导入 -->
        <dependency>
            <groupId>cloud.tianai.captcha</groupId>
            <artifactId>tianai-captcha-springboot-starter</artifactId>
            <version>${captcha.version}</version>
        </dependency>

    </dependencies>

</project>
