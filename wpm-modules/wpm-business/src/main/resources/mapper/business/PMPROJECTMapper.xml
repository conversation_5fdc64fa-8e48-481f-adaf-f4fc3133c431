<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cyth.business.project.mapper.PMPROJECTMapper">

    <select id="selectByCondition" resultType="com.cyth.business.project.domain.vo.PMPROJECTVo"
            parameterType="com.cyth.business.project.domain.vo.PMPROJECTVo">
        Select pro.PMPROJECT_PRJID,
               pro.PMPROJECT_CODE,
               pro.PMPROJECT_NAME,
               pro.PMPROJECT_CREATEBY,
               pro.PMPROJECT_ENTITY,
               org.ASORG_ORGNAME,
               pro.PMPROJECT_CREATEDT,
--                pro.PMPROJECT_STATUS,
               pro.PMPROJECT_MANAGER,
               pro.PMPROJECT_PLANSDATE,
               pq.PMPRJREQ_CUSTID,
               cus.CMCUST_NAME,
               pq.PMPRJREQ_ESTDATE,
               pro.PMPROJECT_MUSTFDATE,
               oa.OAXMKZ11_XMFZGCS oaxmkzgcs,
               oa.OAXMKZ11_HZLX    hzlx,
               oa.OAXMKZ11_XMGM    xmgm,
               oa.OAXMKZ11_XMXS    xmxs,
               oa.OAXMKZ11_GLYQ    glyq,
               oa.OAXMKZ11_JSDW    jsdw,
               oa.OAXMKZ11_ZYZB    zyzb,
               pq.PMPRJREQ_NOTE
        from PMPROJECT pro
                 left join pmprjreq pq
                           on pro.PMPROJECT_PRJID = pq.PMPRJREQ_PRJID
                 left join OAXMKZ11 oa
                           on oa.OAXMKZ11_UUID = pq.PMPRJREQ_PROPERTIESDOCID
                 left join ASORG org
                           on org.ASORG_ORGID = pro.PMPROJECT_ENTITY
                 left join CMCUST cus
                           on cus.CMCUST_CUSTID = pq.PMPRJREQ_CUSTID
        where pro.PMPROJECT_PRJTMPID = '16D5544D-83C7-4D16-A53B-D84CD5BCFB0E' and  pro.PMPROJECT_ENTITY in('17','5')

        /*
          SELECT * from ASSCODE where ASSCODE_CODEID = 'QGCHZLX';  合作类型
          SELECT * from ASSCODE where ASSCODE_CODEID = 'QGCXMXS'; 项目形式
          */
    </select>
</mapper>
