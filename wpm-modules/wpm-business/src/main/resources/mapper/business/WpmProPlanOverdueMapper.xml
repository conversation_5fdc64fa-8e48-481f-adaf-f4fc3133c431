<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cyth.business.plan_overdue.mapper.WpmProPlanOverdueMapper">

    <select id="getPlanOverdueFlowVoById" resultType="com.cyth.business.plan_overdue.domain.vo.PlanOverdueFlowVo"
            parameterType="java.lang.Long">
        SELECT wppo.reason,wppo.task_id,wppo.status, wppo.remark, wppo.handle_emp_id, wp.charge_user_id, wp.task_name, w.pro_name,w.pro_manager_id
        from wpm_pro_plan_overdue wppo
                 inner join wpm_pro_plan wp on wp.pro_id = wppo.pro_id and wp.task_id = wppo.plan_task_id
                 inner join wpm_projects w on wp.pro_id = w.pro_id
        where wppo.id = #{id}
    </select>
</mapper>
