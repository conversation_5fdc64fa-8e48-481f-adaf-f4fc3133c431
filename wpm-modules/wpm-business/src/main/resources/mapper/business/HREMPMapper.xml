<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cyth.business.emp.mapper.HREMPMapper">

    <select id="getList" resultType="com.cyth.business.emp.domain.vo.HREMPVo" parameterType="java.lang.String">
        SELECT prs.PMWBSRESEST_PRJID projectId,
               emp.HREMP_EMAIL email,
               emp.HREMP_EMPID,
               emp.HREMP_NAME,
               emp.HREMP_PHONEC,
               emp.HREMP_EMPCODE,
               emp.HREMP_ORG,
               emp.HREMP_STATUS
        from PMWBSRESEST prs
                 join PMRES res on res.PMRES_RESID = prs.PMWBSRESEST_RESID
                 join HREMP emp on emp.HREMP_EMPID = res.PMRES_RESCODE
        where prs.PMWBSRESEST_PRJID = #{projectId};
    </select>
    <select id="getManagerList" resultType="com.cyth.business.emp.domain.vo.HREMPVo">
        select emp.HREMP_EMPID,
               emp.HREMP_NAME,
               emp.HREMP_PHONEC,
               emp.HREMP_EMAIL email,
               emp.HREMP_EMPCODE,
               emp.HREMP_ORG,
               emp.HREMP_STATUS
        from HREMP emp
        where emp.HREMP_EMPID in (#{managerId}, #{gcs})
    </select>
</mapper>
