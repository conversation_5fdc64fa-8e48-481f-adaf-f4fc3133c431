<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cyth.business.patrol.mapper.WpmProPatrolPicMapper">

    <select id="selectPatrolStatistics" resultType="com.cyth.business.patrol.domain.vo.WpmProPatrolStatisticsVo"
            parameterType="com.cyth.business.plan_change.domain.bo.MonthReportBo">
        SELECT
        xun_type,
        COUNT(CASE WHEN status = 0 THEN 1 END) AS normal_count, -- 正常数
        COUNT(CASE WHEN status = 1 THEN 1 END) AS abnormal_count, -- 非正常数
        COUNT(*) AS total_count -- 总数
        FROM
        wpm_pro_patrol_pic
        WHERE
        pro_id = #{proId}
        <if test="dateStart != null and dateEnd != null">
            <![CDATA[ AND create_time BETWEEN #{dateStart} AND #{dateEnd} ]]>
        </if>
        <if test="dateStart == null or dateStart == ''">
            <![CDATA[ AND create_time <= #{dateEnd} ]]>
        </if>
        GROUP BY
        xun_type;
    </select>
</mapper>
