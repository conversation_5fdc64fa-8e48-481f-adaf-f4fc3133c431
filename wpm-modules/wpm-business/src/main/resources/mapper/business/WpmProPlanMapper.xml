<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cyth.business.plan.mapper.WpmProPlanMapper">

    <select id="getPlanByProId" resultType="com.cyth.business.plan.domain.vo.StatisticsPlanVo"
            parameterType="java.lang.String">
        SELECT *
        from (SELECT
                  task_id,
                     task_name,
                     charge_user_id task_charge,
                     '计划'                                                      time_style,
                     start_status as                                             status,
                     plan_start_time                                             start_date,
                     plan_end_time                                               end_date,
                     DATEDIFF(ifnull(plan_end_time,sysdate()),plan_start_time) day_en
              from wpm_pro_plan
              where pro_id = #{proId}
              union all
              SELECT task_id,
                     task_name,
                     charge_user_id task_charge,
                     '实际'                                                      time_style,
                     end_status as                                               status,
                     real_start_time                                             start_date,
                     real_end_time                                               end_date,
                     DATEDIFF(ifnull(real_end_time,sysdate()),real_start_time) day_en
              from wpm_pro_plan
              where pro_id =#{proId}) x
        order by x.task_id
    </select>
    <select id="monthReport" resultType="com.cyth.business.plan.domain.vo.StatisticsPlanVo"
            parameterType="com.cyth.business.plan_change.domain.bo.MonthReportBo">
        SELECT *
        from (SELECT task_id,
                     task_name,
                     charge_user_id task_charge,
                     '计划'                                                      time_style,
                     start_status as                                             status,
                     plan_start_time                                             start_date,
                     plan_end_time                                               end_date,
                     DATEDIFF(ifnull(plan_end_time,sysdate()),plan_start_time) day_en
              from wpm_pro_plan
              where pro_id = #{proId}
              union all
              SELECT task_id,
                     task_name,
                     charge_user_id task_charge,
                     '实际'                                                      time_style,
                     end_status as                                               status,
                     real_start_time                                             start_date,
                     real_end_time                                               end_date,
                     DATEDIFF(ifnull(real_end_time,sysdate()),real_start_time) day_en
              from wpm_pro_plan
              where pro_id =#{proId}) x
        order by x.task_id
    </select>
</mapper>
