package com.cyth.business.files.domain.bo;


import com.cyth.business.files.domain.WpmProFiles;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 项目资料业务对象 wpm_pro_files
 *
 * <AUTHOR>
 * @date 2024-09-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WpmProFiles.class, reverseConvertGenerate = false)
public class WpmProFilesBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * oss文件id
     */
    //@NotBlank(message = "oss文件id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ossId;

    /**
     * 文件类型
     */
    //@NotBlank(message = "文件类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileType;

    private String[] fileTypes;

    /**
     * 文件编码
     */
    @NotBlank(message = "文件编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileCode;

    /**
     * 文件名称
     */
    @NotBlank(message = "文件名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileName;

    /**
     * 是否获取
     */
    //@NotNull(message = "是否获取不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer isGet;

    /**
     * 状态
     */
    //@NotBlank(message = "状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 项目id
     */
    @NotBlank(message = "项目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String proId;

    /**
     * 项目名称
     */
    @NotBlank(message = "项目名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String proName;

    /**
     * 备注
     */
    //@NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;

    //parent_id
    private Long parentId;

    /**
     * 是否文件
     */
    private Long isFile;

}
