package com.cyth.business.pro_pay.domain;

import com.cyth.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 项目付款信息对象 wpm_pro_payment_info
 *
 * <AUTHOR>
 * @date 2024-11-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wpm_pro_payment_info")
public class WpmProPaymentInfo extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 付款类型
     */
    private String payType;

    /**
     * 付款金额
     */
    private String payAmount;

    /**
     * 付款日期
     */
    private Date payDate;

    /**
     * 项目id
     */
    private String proId;

    /**
     * 备注
     */
    private String remark;


}
