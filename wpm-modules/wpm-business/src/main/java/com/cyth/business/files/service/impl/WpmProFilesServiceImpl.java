package com.cyth.business.files.service.impl;

import cn.hutool.core.io.IoUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cyth.business.file_index.domain.WpmProDocumentIndex;
import com.cyth.business.file_index.domain.bo.WpmProDocumentIndexBo;
import com.cyth.business.file_index.domain.vo.WpmProDocumentIndexVo;
import com.cyth.business.file_index.service.IWpmProDocumentIndexService;
import com.cyth.business.files.domain.WpmProFiles;
import com.cyth.business.files.domain.bo.WpmProFilesBo;
import com.cyth.business.files.domain.vo.WpmProFilesVo;
import com.cyth.business.files.mapper.WpmProFilesMapper;
import com.cyth.business.files.service.IWpmProFilesService;
import com.cyth.common.core.constant.Constants;
import com.cyth.common.core.domain.event.ProcessEvent;
import com.cyth.common.core.domain.event.ProcessTaskEvent;
import com.cyth.common.core.enums.BusinessStatusEnum;
import com.cyth.common.core.utils.MapstructUtils;
import com.cyth.common.core.utils.StringUtils;
import com.cyth.common.core.utils.ValidatorUtils;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.json.utils.JsonUtils;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cyth.common.oss.core.OssClient;
import com.cyth.common.oss.factory.OssFactory;
import com.cyth.system.domain.vo.SysOssVo;
import com.cyth.system.service.ISysOssService;
import com.cyth.workflow.common.constant.ProcessInstanceKeysConstant;
import com.cyth.workflow.domain.TestLeave;
import com.cyth.workflow.domain.bo.StartProcessBo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import software.amazon.awssdk.services.s3.model.S3Object;


import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;


/**
 * 项目资料Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-23
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class WpmProFilesServiceImpl implements IWpmProFilesService {

    private final WpmProFilesMapper baseMapper;

    private final ISysOssService ossService;

    private final IWpmProDocumentIndexService indexService;

    /**
     * 查询项目资料
     *
     * @param id 主键
     * @return 项目资料
     */
    @Override
    public WpmProFilesVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询项目资料列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目资料分页列表
     */
    @Override
    public TableDataInfo<WpmProFilesVo> queryPageList(WpmProFilesBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WpmProFiles> lqw = buildQueryWrapper(bo);
        Page<WpmProFilesVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        result.getRecords().forEach(vo -> {
            if (vo.getFileType() != null) {
                vo.setFileTypes(StringUtils.split(vo.getFileType(), ","));
            }
        });
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的项目资料列表
     *
     * @param bo 查询条件
     * @return 项目资料列表
     */
    @Override
    public List<WpmProFilesVo> queryList(WpmProFilesBo bo) {
        LambdaQueryWrapper<WpmProFiles> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WpmProFiles> buildQueryWrapper(WpmProFilesBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WpmProFiles> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, WpmProFiles::getId, bo.getId());
        lqw.eq(StringUtils.isNotBlank(bo.getOssId()), WpmProFiles::getOssId, bo.getOssId());
        lqw.eq(StringUtils.isNotBlank(bo.getFileType()), WpmProFiles::getFileType, bo.getFileType());
        lqw.eq(StringUtils.isNotBlank(bo.getFileCode()), WpmProFiles::getFileCode, bo.getFileCode());
        lqw.like(StringUtils.isNotBlank(bo.getFileName()), WpmProFiles::getFileName, bo.getFileName());
        lqw.eq(bo.getIsGet() != null, WpmProFiles::getIsGet, bo.getIsGet());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), WpmProFiles::getStatus, bo.getStatus());
        lqw.eq(true, WpmProFiles::getProId, bo.getProId());
        lqw.like(StringUtils.isNotBlank(bo.getProName()), WpmProFiles::getProName, bo.getProName());
        return lqw;
    }

    /**
     * 新增项目资料
     *
     * @param bo 项目资料
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WpmProFilesBo bo) {
        WpmProFiles add = MapstructUtils.convert(bo, WpmProFiles.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改项目资料
     *
     * @param bo 项目资料
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WpmProFilesBo bo) {
        WpmProFiles update = MapstructUtils.convert(bo, WpmProFiles.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WpmProFiles entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除项目资料信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 初始化文件目录 过度用
     *
     * @return
     */
    @Override
    public List<WpmProFilesBo> init() {
        String index = "[{\n" +
            "\t\t\"fileCode\": \"一\",\n" +
            "\t\t\"fileName\": \"工程投资决策阶段\",\n" +
            "\t\t\"parentId\": 0\n" +
            "\t},\n" +
            "\t{\n" +
            "\t\t\"fileCode\": \"1\",\n" +
            "\t\t\"fileName\": \"项目复议书\",\n" +
            "\t\t\"parentId\": 1\n" +
            "\t},\n" +
            "\t{\n" +
            "\t\t\"fileCode\": \"6\",\n" +
            "\t\t\"fileName\": \"可批复、立项批复、初步设计批复(含估算或概算明细)\",\n" +
            "\t\t\"parentId\": 1\n" +
            "\t}, {\n" +
            "\t\t\"fileCode\": \"三\",\n" +
            "\t\t\"fileName\": \"招投标文件及合同\",\n" +
            "\t\t\"parentId\": 0\n" +
            "\t}, {\n" +
            "\t\t\"fileCode\": \"1\",\n" +
            "\t\t\"fileName\": \"勘察、设计招标文件、中标通知书\",\n" +
            "\t\t\"parentId\": 3\n" +
            "\t}, {\n" +
            "\t\t\"fileCode\": \"5\",\n" +
            "\t\t\"fileName\": \"施工承包书及补充协议\",\n" +
            "\t\t\"parentId\": 3\n" +
            "\t}, {\n" +
            "\t\t\"fileCode\": \"6\",\n" +
            "\t\t\"fileName\": \"工程监理招标文件、中标通知书\",\n" +
            "\t\t\"parentId\": 3\n" +
            "\t}, {\n" +
            "\t\t\"fileCode\": \"7\",\n" +
            "\t\t\"fileName\": \"监理委托合同\",\n" +
            "\t\t\"parentId\": 3\n" +
            "\t}\n" +
            "]";
        return JsonUtils.parseArray(index, WpmProFilesBo.class);
    }

    @Override
    @Transactional
    public void updateIndex(WpmProFilesBo bo) {
        WpmProDocumentIndexBo indexBo = new WpmProDocumentIndexBo();
        indexBo.setIsDelete(Constants.INTEGER_ZERO);
        List<WpmProDocumentIndexVo> indexVos = indexService.queryList(indexBo);
        indexVos.forEach(indexVo -> {
            WpmProFilesBo add = new WpmProFilesBo();
            add.setFileCode(indexVo.getCode());
            add.setFileName(indexVo.getName());
            add.setFileType(indexVo.getFileType());
            add.setParentId(indexVo.getParentId());
            add.setProId(bo.getProId());
            add.setProName(bo.getProName());
            add.setIsFile(indexVo.getIsFile());
            //add.setStatus(BusinessStatusEnum.DRAFT.getStatus());
            ValidatorUtils.validate(add, AddGroup.class);
            WpmProFilesVo filesVo = baseMapper.selectVoOne(new LambdaQueryWrapper<>(WpmProFiles.class).
                eq(WpmProFiles::getProId, bo.getProId()).eq(WpmProFiles::getFileName, add.getFileName()).eq(WpmProFiles::getFileCode, add.getFileCode()));
            if (filesVo == null) {
                baseMapper.insert(MapstructUtils.convert(add, WpmProFiles.class));
            }else {
                baseMapper.update(null, new LambdaUpdateWrapper<WpmProFiles>().
                    set(WpmProFiles::getParentId, add.getParentId()).
                    set(WpmProFiles::getIsFile, add.getIsFile()).
                    set(WpmProFiles::getFileType, add.getFileType()).
                    set(WpmProFiles::getFileCode, add.getFileCode()).
                    set(WpmProFiles::getFileName, add.getFileName()).eq(WpmProFiles::getId, filesVo.getId()));
            }
        });
    }

    @Override
    public void updateFileType(WpmProFilesBo bo) {
        baseMapper.update(null, new LambdaUpdateWrapper<WpmProFiles>().eq(WpmProFiles::getId, bo.getId())
            .set(WpmProFiles::getFileType, StringUtils.join(bo.getFileTypes(), ",")));
    }

    @Override
    public int updateFileOssid(WpmProFilesBo bo) {
        return baseMapper.update(null, new LambdaUpdateWrapper<WpmProFiles>().eq(WpmProFiles::getId, bo.getId())
            .set(WpmProFiles::getOssId, bo.getOssId()));
    }

    @Override
    @Transactional
    public Boolean handleDelete(List<Long> ids) {
        //删除文件
        List<Long> ossIds = new ArrayList<>();
        baseMapper.selectBatchIds(ids).stream()
            .map(WpmProFiles::getOssId)
            .filter(Objects::nonNull)
            .flatMap(oss -> Arrays.stream(StringUtils.split(oss, ",")))
            .map(Long::parseLong)
            .forEach(ossIds::add);
        ossService.deleteWithValidByIds(ossIds, false);
        return baseMapper.updateBatchById(ids.stream().map(id -> {
            WpmProFiles files = new WpmProFiles();
            files.setId(id);
            files.setOssId(null);
            return files;
        }).collect(Collectors.toList()));
    }

    @Override
    public int updateRemark(WpmProFilesBo bo) {
        return baseMapper.update(null, new LambdaUpdateWrapper<WpmProFiles>().eq(WpmProFiles::getId, bo.getId()).set(WpmProFiles::getRemark, bo.getRemark()));
    }

    @Override
    public byte[] batchDownload(List<Long> ids) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ZipOutputStream zip = new ZipOutputStream(outputStream);
        for (Long id : ids) {
            WpmProFilesVo filesVo = baseMapper.selectVoById(id);
            if (filesVo != null) {
                List<Long> ossIds = Arrays.stream(StringUtils.split(filesVo.getOssId(), ",")).map(Long::parseLong).toList();
                ossIds.stream().map(ossService::getById).filter(Objects::nonNull).forEach(ossVo -> {
                    OssClient storage = OssFactory.instance(ossVo.getService());
                    try {
                        zip.putNextEntry(new ZipEntry(filesVo.getFileName() + "_" + ossVo.getFileName()));
                        zip.write(IoUtil.readBytes(storage.getObjectContent(ossVo.getUrl())));
                        zip.closeEntry();
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                });
            }
        }
        zip.close();
        return outputStream.toByteArray();
    }

    @Override
    public void updatePreFileIsGet(Long proFileId) {
        baseMapper.update(null, new LambdaUpdateWrapper<WpmProFiles>().eq(WpmProFiles::getId, proFileId).set(WpmProFiles::getIsGet, 1));
    }
}

