package com.cyth.business.plan.mapper;


import com.cyth.business.plan.domain.WpmProPlan;
import com.cyth.business.plan.domain.vo.StatisticsPlanVo;
import com.cyth.business.plan.domain.vo.WpmProPlanVo;
import com.cyth.business.plan_change.domain.bo.MonthReportBo;
import com.cyth.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 项目计划Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-18
 */
public interface WpmProPlanMapper extends BaseMapperPlus<WpmProPlan, WpmProPlanVo> {
    List<StatisticsPlanVo> getPlanByProId(String proId);

     List<StatisticsPlanVo> monthReport(MonthReportBo bo);
}
