package com.cyth.business.pro_pay.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.cyth.business.enums.pro.PayTypeEnum;
import com.cyth.business.plan_change.domain.bo.MonthReportBo;
import com.cyth.business.pro_pay.domain.WpmProPaymentInfo;
import com.cyth.business.pro_pay.domain.bo.WpmProPaymentInfoBo;
import com.cyth.business.pro_pay.domain.vo.PaymentInfoMonthVo;
import com.cyth.business.pro_pay.domain.vo.WpmProPaymentInfoVo;
import com.cyth.business.pro_pay.mapper.WpmProPaymentInfoMapper;
import com.cyth.business.pro_pay.service.IWpmProPaymentInfoService;
import com.cyth.business.project.domain.vo.WpmProjectsVo;
import com.cyth.business.project.service.IWpmProjectsService;
import com.cyth.common.core.exception.ServiceException;
import com.cyth.common.core.utils.MapstructUtils;
import com.cyth.common.core.utils.NumberUtils;
import com.cyth.common.core.utils.StringUtils;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import java.math.BigDecimal;
import java.util.*;

/**
 * 项目付款信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-08
 */
@RequiredArgsConstructor
@Service
public class WpmProPaymentInfoServiceImpl implements IWpmProPaymentInfoService {

    private final WpmProPaymentInfoMapper baseMapper;

    private final IWpmProjectsService projectsService;

    /**
     * 查询项目付款信息
     *
     * @param id 主键
     * @return 项目付款信息
     */
    @Override
    public WpmProPaymentInfoVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询项目付款信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目付款信息分页列表
     */
    @Override
    public TableDataInfo<WpmProPaymentInfoVo> queryPageList(WpmProPaymentInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WpmProPaymentInfo> lqw = buildQueryWrapper(bo);
        Page<WpmProPaymentInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的项目付款信息列表
     *
     * @param bo 查询条件
     * @return 项目付款信息列表
     */
    @Override
    public List<WpmProPaymentInfoVo> queryList(WpmProPaymentInfoBo bo) {
        LambdaQueryWrapper<WpmProPaymentInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WpmProPaymentInfo> buildQueryWrapper(WpmProPaymentInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WpmProPaymentInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getPayType() != null, WpmProPaymentInfo::getPayType, bo.getPayType());
        lqw.eq(StringUtils.isNotBlank(bo.getPayAmount()), WpmProPaymentInfo::getPayAmount, bo.getPayAmount());
        lqw.eq(bo.getPayDate() != null, WpmProPaymentInfo::getPayDate, bo.getPayDate());
        lqw.eq(StringUtils.isNotBlank(bo.getProId()), WpmProPaymentInfo::getProId, bo.getProId());
        return lqw;
    }

    /**
     * 新增项目付款信息
     *
     * @param bo 项目付款信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WpmProPaymentInfoBo bo) {
        WpmProPaymentInfo add = MapstructUtils.convert(bo, WpmProPaymentInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改项目付款信息
     *
     * @param bo 项目付款信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WpmProPaymentInfoBo bo) {
        WpmProPaymentInfo update = MapstructUtils.convert(bo, WpmProPaymentInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WpmProPaymentInfo entity) {
        //检验付款是不是超过总价款
        WpmProjectsVo projectsVo = projectsService.selectByProId(entity.getProId());
        if (projectsVo != null) {
            if (StringUtils.isEmpty(projectsVo.getTotalInvestment())) {
                throw new ServiceException("项目总投资为空");
            }
            List<WpmProPaymentInfoVo> paymentInfoVos = baseMapper.selectVoList(Wrappers.lambdaQuery(WpmProPaymentInfo.class).eq(WpmProPaymentInfo::getProId, entity.getProId()));
            BigDecimal total = new BigDecimal(projectsVo.getContractPrice());
            BigDecimal pay = new BigDecimal(0);
            for (WpmProPaymentInfoVo paymentInfoVo : paymentInfoVos) {
                pay = pay.add(new BigDecimal(paymentInfoVo.getPayAmount()));
            }
            pay = pay.add(new BigDecimal(entity.getPayAmount()));
            if (pay.compareTo(total) > 0) {
                throw new ServiceException("付款金额超过项目合同价");
            }
        }
    }

    /**
     * 校验并批量删除项目付款信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public PaymentInfoMonthVo queryPaymentInfoMonth(MonthReportBo bo) {
        WpmProjectsVo projectsVo = projectsService.selectByProId(bo.getProId());
        if (projectsVo != null&&StringUtils.isNotBlank(projectsVo.getTotalInvestment())) {
            PaymentInfoMonthVo monthVo = new PaymentInfoMonthVo();
            monthVo.setTotalInvestment(projectsVo.getTotalInvestment());
            monthVo.setContractPrice(projectsVo.getContractPrice());
            monthVo.setControlPrice(projectsVo.getControlPrice());
            List<WpmProPaymentInfo> paymentInfoVos = baseMapper.selectList(Wrappers.lambdaQuery(WpmProPaymentInfo.class).
                eq(WpmProPaymentInfo::getProId, bo.getProId()).le(WpmProPaymentInfo::getPayDate, bo.getDateEnd()).orderByAsc(WpmProPaymentInfo::getPayDate));
            Set<String> payStr = new HashSet<>();
            BigDecimal total = new BigDecimal(0);
            if (paymentInfoVos != null) {
                for (WpmProPaymentInfo paymentInfo : paymentInfoVos) {
                    if (PayTypeEnum.deposit.getCode().equals(paymentInfo.getPayType())) {
                        monthVo.setPrePayment(paymentInfo.getPayAmount());
                    } else {
                        payStr.add((paymentInfo.getPayAmount()));
                    }
                    total = total.add(new BigDecimal(paymentInfo.getPayAmount()));
                }
            }
            monthVo.setPaid(total.toString());
            BigDecimal percentage  = NumberUtil.div(total.toString(), projectsVo.getContractPrice());
            monthVo.setPaymentProgress(NumberUtil.roundStr( percentage.doubleValue(), 2));
            monthVo.setPaymentRecord(payStr);
            return monthVo;
        }
        return null;
    }
}
