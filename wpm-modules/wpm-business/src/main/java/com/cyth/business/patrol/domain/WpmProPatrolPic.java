package com.cyth.business.patrol.domain;

import com.cyth.common.mybatis.core.domain.BaseEntity;
import com.cyth.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 巡检图片对象 wpm_pro_patrol_pic
 *
 * <AUTHOR>
 * @date 2024-10-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wpm_pro_patrol_pic")
public class WpmProPatrolPic extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 项目ID
     */
    private String proId;

    /**
     * 巡检计划任务id
     */
    private Long planTaskId;

    /**
     * 图片ossId
     */
    private String ossIds;
    /**
     * 状态
     */
    private Long status;

    /**
     * 备注
     */
    private String remark;
    /**
     * 巡检类型
     */
    private String xunType;
    //audit_status
    private String auditStatus;

    private String taskId;

    private String rectify;
}
