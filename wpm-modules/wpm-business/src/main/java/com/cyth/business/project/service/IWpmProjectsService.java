package com.cyth.business.project.service;


import com.cyth.business.emp.domain.bo.WpmProEmpBo;
import com.cyth.business.project.domain.bo.WpmProjectsBo;
import com.cyth.business.project.domain.vo.WpmProjectsVo;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.cyth.system.domain.bo.SysUserBo;

import java.util.Collection;
import java.util.List;

/**
 * 项目Service接口
 *
 * <AUTHOR>
 * @date 2024-09-13
 */
public interface IWpmProjectsService {

    /**
     * 查询项目
     *
     * @param id 主键
     * @return 项目
     */
    WpmProjectsVo queryById(Long id);

    /**
     * 分页查询项目列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目分页列表
     */
    TableDataInfo<WpmProjectsVo> queryPageList(WpmProjectsBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的项目列表
     *
     * @param bo 查询条件
     * @return 项目列表
     */
    List<WpmProjectsVo> queryList(WpmProjectsBo bo);

    /**
     * 新增项目
     *
     * @param bo 项目
     * @return 是否新增成功
     */
    Boolean insertByBo(WpmProjectsBo bo);

    /**
     * 修改项目
     *
     * @param bo 项目
     * @return 是否修改成功
     */
    Boolean updateByBo(WpmProjectsBo bo);

    /**
     * 校验并批量删除项目信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    TableDataInfo<WpmProjectsVo> syncFromNoma(WpmProjectsBo bp,PageQuery pageQuery);

    Boolean insertFromNoma(List<WpmProjectsVo> list, List<SysUserBo> sysUserBos, List<WpmProEmpBo> empBos);

    void updateTaskStatus(String proId, int code);

    int updateProjectStatus(WpmProjectsBo bo);

    int proStart(Long id);

    int proFinish(Long id);

    WpmProjectsVo selectByProId(String proId);


    int proReStart(Long id);
}
