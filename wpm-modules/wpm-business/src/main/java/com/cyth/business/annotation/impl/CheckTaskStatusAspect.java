package com.cyth.business.annotation.impl;

import com.cyth.business.annotation.CheckTaskStatus;
import com.cyth.common.core.utils.SpringUtils;
import com.cyth.common.idempotent.annotation.RepeatSubmit;
import com.cyth.common.log.annotation.Log;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.sql.rowset.serial.SerialException;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.Parameter;

/**
 * <AUTHOR>
 * @version 版本号
 * @date 2024年09月20日 17:32
 * @description
 */
@Aspect
@Component
@Slf4j
public class CheckTaskStatusAspect {

    @Before("@annotation(checkTaskStatus)")
    public void checkTaskStatus(JoinPoint point, CheckTaskStatus checkTaskStatus)throws Throwable{
        MethodSignature signature = (MethodSignature) point.getSignature();
        Parameter[] parameters = signature.getMethod().getParameters();
        Class<?> entityClass = checkTaskStatus.value();
        for (int i = 0; i < parameters.length; i++) {
            if (parameters[i].getType().equals(entityClass)) {
                log.info("checkTaskStatus run start");
                Object arg = point.getArgs()[i];
                Object itemId = getField(arg,"proId");
                if (itemId == null){
                    throw new SerialException("参数错误");
                }
                log.info("checkTaskStatus proId="+getField(arg,"proId"));
                // TODO: 2024/9/20
                // 检查任务状态
                Object bean = SpringUtils.getBean("wpmProjectsServiceImpl");
                // 如果任务状态不合法，抛出异常
                Object entity = bean.getClass().getMethod("selectByProId", String.class).invoke(bean, itemId.toString());
                if (entity == null) {
                    throw new SerialException("参数错误");
                }
                if (!getField(entity,"taskStatus").toString().equals("2")) {
                    throw new SerialException("任务未完成，不能操作");
                }
            }
        }

    }
    private static Object getField(Object obj, String fieldName) throws Exception {
        Class<?> clazz = obj.getClass();
        Field field = clazz.getDeclaredField(fieldName);

        // 设置可访问性，以便访问私有字段
        field.setAccessible(true);

        // 获取字段的值
        return field.get(obj);
    }
    @AfterReturning(pointcut = "@annotation(checkTaskStatus)", returning = "jsonResult")
    public void doAfterReturning(JoinPoint joinPoint, CheckTaskStatus checkTaskStatus, Object jsonResult) {
        log.info("checkTaskStatus run end");
    }

    @AfterThrowing(value = "@annotation(checkTaskStatus)", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, CheckTaskStatus checkTaskStatus, Exception e) {
       if (e !=null){
           log.error("checkTaskStatus run error",e);
       }
    }
}
