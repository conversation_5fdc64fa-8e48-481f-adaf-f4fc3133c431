package com.cyth.business.plan_change.domain.bo;

import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 版本号
 * @date 2024年10月31日 15:21
 * @description
 */
@Data
public class MonthReportBo implements Serializable {
    @NotBlank(message = "项目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String proId;
    @NotNull(message = "开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String  dateStart;
    @NotNull(message = "结束时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String dateEnd;
}
