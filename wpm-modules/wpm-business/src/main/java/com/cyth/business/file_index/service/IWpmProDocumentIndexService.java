package com.cyth.business.file_index.service;


import com.cyth.business.file_index.domain.bo.WpmProDocumentIndexBo;
import com.cyth.business.file_index.domain.vo.WpmProDocumentIndexVo;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 项目资料目录Service接口
 *
 * <AUTHOR>
 * @date 2024-10-16
 */
public interface IWpmProDocumentIndexService {

    /**
     * 查询项目资料目录
     *
     * @param id 主键
     * @return 项目资料目录
     */
    WpmProDocumentIndexVo queryById(Long id);

    /**
     * 分页查询项目资料目录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目资料目录分页列表
     */
    TableDataInfo<WpmProDocumentIndexVo> queryPageList(WpmProDocumentIndexBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的项目资料目录列表
     *
     * @param bo 查询条件
     * @return 项目资料目录列表
     */
    List<WpmProDocumentIndexVo> queryList(WpmProDocumentIndexBo bo);

    /**
     * 新增项目资料目录
     *
     * @param bo 项目资料目录
     * @return 是否新增成功
     */
    Boolean insertByBo(WpmProDocumentIndexBo bo);

    /**
     * 修改项目资料目录
     *
     * @param bo 项目资料目录
     * @return 是否修改成功
     */
    Boolean updateByBo(WpmProDocumentIndexBo bo);

    /**
     * 校验并批量删除项目资料目录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
