package com.cyth.business.project.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cyth.business.project.domain.vo.PMPROJECTVo;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 项目Service接口
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
public interface IPMPROJECTService {


    TableDataInfo<PMPROJECTVo> queryList(PageQuery pageQuery,PMPROJECTVo pmprojectVo);
}
