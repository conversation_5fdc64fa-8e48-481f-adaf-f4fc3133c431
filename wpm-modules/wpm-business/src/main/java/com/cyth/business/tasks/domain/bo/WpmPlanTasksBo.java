package com.cyth.business.tasks.domain.bo;


import com.cyth.business.tasks.domain.WpmPlanTasks;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 任务业务对象 wpm_pla_tasks
 *
 * <AUTHOR>
 * @date 2024-09-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WpmPlanTasks.class, reverseConvertGenerate = false)
public class WpmPlanTasksBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String taskName;


    /**
     * 父任务id
     */
    @NotNull(message = "父任务id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long parentId;

    /**
     * 任务状态
     */
    //@NotNull(message = "任务状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long taskStatus;

    /**
     * 备注
     */
    //@NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;

    private Integer orderNum;
}
