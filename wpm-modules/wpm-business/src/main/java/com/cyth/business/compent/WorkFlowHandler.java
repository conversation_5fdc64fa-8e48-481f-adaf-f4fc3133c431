package com.cyth.business.compent;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cyth.business.enums.pro.TaskStatusEnum;
import com.cyth.business.plan.domain.WpmProPlan;
import com.cyth.business.plan.service.IWpmProPlanService;
import com.cyth.business.plan_change.domain.WpmProPlanChange;
import com.cyth.business.plan_change.domain.vo.WpmProPlanChangeVo;
import com.cyth.business.plan_change.service.IWpmProPlanChangeService;
import com.cyth.business.plan_history.domain.WpmProPlanHistory;
import com.cyth.business.plan_history.service.IWpmProPlanHistoryService;
import com.cyth.business.project.domain.WpmProjects;
import com.cyth.business.project.mapper.WpmProjectsMapper;
import com.cyth.common.core.domain.event.ProcessEvent;
import com.cyth.common.core.domain.event.ProcessTaskEvent;
import com.cyth.common.core.enums.BusinessStatusEnum;
import com.cyth.workflow.common.constant.ProcessInstanceKeysConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @version 版本号
 * @date 2024年10月22日 16:22
 * @description
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WorkFlowHandler {

    private final IWpmProPlanHistoryService historyService;

    private final IWpmProPlanChangeService planChangeService;

    private final WpmProjectsMapper baseMapper;

    private final IWpmProPlanService planService;

    /**
     * 总体流程监听(例如: 提交 退回 撤销 终止 作废等)
     * 正常使用只需#processEvent.key=='leave1'
     * 示例为了方便则使用startsWith匹配了全部示例key
     *
     * @param processEvent 参数
     */
    @org.springframework.context.event.EventListener(condition = "#processEvent.key.equals('" + ProcessInstanceKeysConstant.PRO_PLAN_TASK_CHANGE + "')")
    @Transactional
    public void processHandler(ProcessEvent processEvent) {
        log.info("processHandler当前任务执行了{}", processEvent.toString());
        WpmProPlanHistory history = historyService.selectById(Long.valueOf(processEvent.getBusinessKey()));
        history.setStatus(processEvent.getStatus());
        if (processEvent.isSubmit()) {
            history.setStatus(BusinessStatusEnum.WAITING.getStatus());
        }
        if (processEvent.getStatus().equals(BusinessStatusEnum.FINISH.getStatus())) {
            //projects.setTaskStatus((long) TaskStatusEnum.SUCCESS.getCode());
            baseMapper.update(new LambdaUpdateWrapper<WpmProjects>().eq(WpmProjects::getProId, history.getProId()).set(WpmProjects::getTaskStatus, TaskStatusEnum.SUCCESS.getCode()));
            //修改plan
            List<WpmProPlanChangeVo> changeVos = planChangeService.getByLambda(new LambdaQueryWrapper<WpmProPlanChange>().eq(WpmProPlanChange::getProId, history.getProId()).eq(WpmProPlanChange::getTaskId, history.getTaskId()));
            if (CollectionUtils.isNotEmpty(changeVos)) {
                changeVos.forEach(changeVo -> {
                    if ("负责人".equals(changeVo.getChangeColumn())) {
                        planService.updateLambda(new LambdaUpdateWrapper<WpmProPlan>().eq(WpmProPlan::getId, changeVo.getPlanId()).eq(WpmProPlan::getProId, history.getProId()).set(WpmProPlan::getChargeUserId, changeVo.getChangeAfter()));
                    }
                    if ("外部成员".equals(changeVo.getChangeColumn())) {
                        planService.updateLambda(new LambdaUpdateWrapper<WpmProPlan>().eq(WpmProPlan::getId, changeVo.getPlanId()).eq(WpmProPlan::getProId, history.getProId()).set(WpmProPlan::getExternalUserId, changeVo.getChangeAfter()));
                    }
                    if ("计划开工时间".equals(changeVo.getChangeColumn())) {
                        planService.updateLambda(new LambdaUpdateWrapper<WpmProPlan>().eq(WpmProPlan::getId, changeVo.getPlanId()).eq(WpmProPlan::getProId, history.getProId()).set(WpmProPlan::getPlanStartTime, DateUtil.parse(changeVo.getChangeAfter()) ));
                    }
                    if ("计划完工时间".equals(changeVo.getChangeColumn())) {
                        planService.updateLambda(new LambdaUpdateWrapper<WpmProPlan>().eq(WpmProPlan::getId, changeVo.getPlanId()).eq(WpmProPlan::getProId, history.getProId()).set(WpmProPlan::getPlanEndTime, DateUtil.parse(changeVo.getChangeAfter()) ));
                    }
                });

            }
        }
        historyService.updateById(history);
    }

    /**
     * 执行办理任务监听
     * 示例：也可通过  @EventListener(condition = "#processTaskEvent.key=='leave1'")进行判断
     * 在方法中判断流程节点key
     * if ("xxx".equals(processTaskEvent.getTaskDefinitionKey())) {
     * //执行业务逻辑
     * }
     *
     * @param processTaskEvent 参数
     */
    //@EventListener(condition = "#processTaskEvent.key=='leave1' && #processTaskEvent.taskDefinitionKey=='Activity_14633hx'")
    @EventListener(condition = "#processTaskEvent.key=='" + ProcessInstanceKeysConstant.PRO_PLAN_TASK_CHANGE + "'")
    public void processTaskHandler(ProcessTaskEvent processTaskEvent) {
        log.info("processTaskHandler当前任务执行了{}", processTaskEvent.toString());
        WpmProPlanHistory history = historyService.selectById(Long.valueOf(processTaskEvent.getBusinessKey()));
        history.setStatus(BusinessStatusEnum.WAITING.getStatus());
        //history.setTaskId(processTaskEvent.getTaskId());
        historyService.updateById(history);
    }
}
