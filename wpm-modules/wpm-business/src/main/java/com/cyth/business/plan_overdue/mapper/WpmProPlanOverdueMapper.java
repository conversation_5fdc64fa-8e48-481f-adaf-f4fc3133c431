package com.cyth.business.plan_overdue.mapper;

import com.cyth.business.plan_overdue.domain.WpmProPlanOverdue;
import com.cyth.business.plan_overdue.domain.vo.PlanOverdueFlowVo;
import com.cyth.business.plan_overdue.domain.vo.WpmProPlanOverdueVo;
import com.cyth.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

/**
 * 项目超期Mapper接口
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
public interface WpmProPlanOverdueMapper extends BaseMapperPlus<WpmProPlanOverdue, WpmProPlanOverdueVo> {
    PlanOverdueFlowVo getPlanOverdueFlowVoById(@Param("id") Long id);
}
