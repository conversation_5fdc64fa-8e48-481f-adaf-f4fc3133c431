package com.cyth.business.plan_change.service;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cyth.business.plan_change.domain.WpmProPlanChange;
import com.cyth.business.plan_change.domain.bo.WpmProPlanChangeBo;
import com.cyth.business.plan_change.domain.bo.MonthReportBo;
import com.cyth.business.plan_change.domain.vo.WpmProPlanChangeVo;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 项目计划变更Service接口
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
public interface IWpmProPlanChangeService {

    /**
     * 查询项目计划变更
     *
     * @param id 主键
     * @return 项目计划变更
     */
    WpmProPlanChangeVo queryById(Long id);

    /**
     * 分页查询项目计划变更列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目计划变更分页列表
     */
    TableDataInfo<WpmProPlanChangeVo> queryPageList(WpmProPlanChangeBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的项目计划变更列表
     *
     * @param bo 查询条件
     * @return 项目计划变更列表
     */
    List<WpmProPlanChangeVo> queryList(WpmProPlanChangeBo bo);

    /**
     * 新增项目计划变更
     *
     * @param bo 项目计划变更
     * @return 是否新增成功
     */
    Boolean insertByBo(WpmProPlanChangeBo bo);

    /**
     * 修改项目计划变更
     *
     * @param bo 项目计划变更
     * @return 是否修改成功
     */
    Boolean updateByBo(WpmProPlanChangeBo bo);

    /**
     * 校验并批量删除项目计划变更信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    List<WpmProPlanChangeVo> getByLambda(LambdaQueryWrapper<WpmProPlanChange> eq);

    List<WpmProPlanChangeVo> queryListByProId(MonthReportBo bo);
}
