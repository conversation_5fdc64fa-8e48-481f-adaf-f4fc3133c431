package com.cyth.business.plan_overdue.domain.bo;


import com.cyth.business.plan_overdue.domain.WpmProPlanOverdue;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 项目超期业务对象 wpm_pro_plan_overdue
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WpmProPlanOverdue.class, reverseConvertGenerate = false)
public class WpmProPlanOverdueBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 项目编号
     */
    @NotBlank(message = "项目编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String proId;

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String taskName;

    /**
     * 计划任务编号
     */
    @NotNull(message = "计划任务编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long planTaskId;

    /**
     * 处理人
     */
    //@NotNull(message = "处理人不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long handleId;

    /**
     * 处理人id
     */
    @NotBlank(message = "处理人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String handleEmpId;

    /**
     * 工作流任务编号
     */
    //@NotNull(message = "工作流任务编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long taskId;

    /**
     * 公告内容
     */
    //@NotBlank(message = "公告内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String reason;

    /**
     * 工作流状态
     */
    //@NotBlank(message = "工作流状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 备注
     */
    //@NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
