package com.cyth.business.project.domain.vo;

import java.util.Date;

import com.cyth.business.project.domain.WpmProjects;
import com.cyth.common.json.anno.PriceFormatter;
import com.cyth.common.translation.annotation.Translation;
import com.cyth.common.translation.constant.TransConstant;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cyth.common.excel.annotation.ExcelDictFormat;
import com.cyth.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 项目视图对象 wpm_projects
 *
 * <AUTHOR>
 * @date 2024-09-13
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WpmProjects.class)
public class WpmProjectsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 项目id，来之诺明
     */
    @ExcelProperty(value = "项目id，来之诺明")
    private String proId;

    /**
     * 项目编码
     */
    @ExcelProperty(value = "项目编码")
    private String proCode;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String proName;

    /**
     * 部门id
     */
    @ExcelProperty(value = "部门id")
    private String proDeptId;

    /**
     * 部门名称
     */
    @ExcelProperty(value = "部门名称")
    private String proDeptName;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private Long proStatus;
    private Long complateStatus;
    private Long taskStatus;
    private String taskId;
    //audit_status
    private String auditStatus;


    /**
     * 项目负责人
     */
    @ExcelProperty(value = "项目负责人")
    private String proManagerId;
    @Translation(type = TransConstant.EMPLOYEE_ID_TO_NAME, mapper = "proManagerId")
    private String proManagerName;

    /**
     * 计划开始时间
     */
    @ExcelProperty(value = "计划开始时间")
    private Date proPlanStartTime;

    /**
     * 实际开始时间
     */
    @ExcelProperty(value = "实际开始时间")
    private Date proRealStartTime;

    /**
     * 必须完成时间
     */
    @ExcelProperty(value = "必须完成时间")
    private Date proFinishTime;

    /**
     * 实际完成时间
     */
    @ExcelProperty(value = "实际完成时间")
    private Date proRealFinishTime;

    /**
     * 客户id
     */
    @ExcelProperty(value = "客户id")
    private String proCustomId;

    /**
     * 客户名称
     */
    @ExcelProperty(value = "客户名称")
    private String proCustomName;

    /**
     * 立项时间
     */
    @ExcelProperty(value = "立项时间")
    private Date proEsTime;

    /**
     * 负责工程师
     */
    @ExcelProperty(value = "负责工程师")
    private String fzgcs;

    @Translation(type = TransConstant.EMPLOYEE_ID_TO_NAME, mapper = "fzgcs")
    private String chargeEngineer;

    /**
     * 合作类型
     */
    @ExcelProperty(value = "合作类型")
    private String proCoType;

    /**
     * 项目规模
     */
    @ExcelProperty(value = "项目规模")
    private String proScale;

    /**
     * 项目形式
     */
    @ExcelProperty(value = "项目形式")
    private String proForm;

    /**
     * 管理要求
     */
    @ExcelProperty(value = "管理要求")
    private String manageNeeds;

    /**
     * 建设单位
     */
    @ExcelProperty(value = "建设单位")
    private String constructCompany;

    /**
     * 主要指标
     */
    @ExcelProperty(value = "主要指标")
    private String mainIndicator;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    private int empNum;

    //total_investment总投资金额
    @PriceFormatter
    private String totalInvestment;

    //control_price控制价
    @PriceFormatter
    private String controlPrice;
    //contract_price合同价
    @PriceFormatter
    private String contractPrice;
}
