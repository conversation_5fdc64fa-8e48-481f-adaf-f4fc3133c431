package com.cyth.business.files.domain.vo;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cyth.business.files.domain.WpmProFiles;
import com.cyth.common.excel.annotation.ExcelDictFormat;
import com.cyth.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 项目资料视图对象 wpm_pro_files
 *
 * <AUTHOR>
 * @date 2024-09-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WpmProFiles.class)
public class WpmProFilesVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * oss文件id
     */
    @ExcelProperty(value = "oss文件id")
    private String ossId;

    /**
     * 文件类型
     */
    @ExcelProperty(value = "文件类型")
    private String fileType;

    private String[] fileTypes;

    /**
     * 文件编码
     */
    @ExcelProperty(value = "文件编码")
    private String fileCode;

    /**
     * 文件名称
     */
    @ExcelProperty(value = "文件名称")
    private String fileName;

    /**
     * 是否获取
     */
    @ExcelProperty(value = "是否获取")
    private Integer isGet;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private String proId;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称")
    private String proName;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    //parent_id
    private Long parentId;

    private String taskId;

    /**
     * 是否文件
     */
    private Long isFile;
}
