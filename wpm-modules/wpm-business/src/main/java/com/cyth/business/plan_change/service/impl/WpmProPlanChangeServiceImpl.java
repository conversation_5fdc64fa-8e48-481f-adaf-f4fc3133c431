package com.cyth.business.plan_change.service.impl;

import com.cyth.business.plan_change.domain.WpmProPlanChange;
import com.cyth.business.plan_change.domain.bo.WpmProPlanChangeBo;
import com.cyth.business.plan_change.domain.bo.MonthReportBo;
import com.cyth.business.plan_change.domain.vo.WpmProPlanChangeVo;
import com.cyth.business.plan_change.mapper.WpmProPlanChangeMapper;
import com.cyth.business.plan_change.service.IWpmProPlanChangeService;
import com.cyth.common.core.utils.MapstructUtils;
import com.cyth.common.core.utils.StringUtils;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cyth.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 项目计划变更Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
@RequiredArgsConstructor
@Service
public class WpmProPlanChangeServiceImpl implements IWpmProPlanChangeService {

    private final WpmProPlanChangeMapper baseMapper;

    private final ISysUserService userService;

    /**
     * 查询项目计划变更
     *
     * @param id 主键
     * @return 项目计划变更
     */
    @Override
    public WpmProPlanChangeVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询项目计划变更列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目计划变更分页列表
     */
    @Override
    public TableDataInfo<WpmProPlanChangeVo> queryPageList(WpmProPlanChangeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WpmProPlanChange> lqw = buildQueryWrapper(bo);
        Page<WpmProPlanChangeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的项目计划变更列表
     *
     * @param bo 查询条件
     * @return 项目计划变更列表
     */
    @Override
    public List<WpmProPlanChangeVo> queryList(WpmProPlanChangeBo bo) {
        LambdaQueryWrapper<WpmProPlanChange> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WpmProPlanChange> buildQueryWrapper(WpmProPlanChangeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WpmProPlanChange> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getProId()), WpmProPlanChange::getProId, bo.getProId());
        lqw.eq(bo.getPlanId() != null, WpmProPlanChange::getPlanId, bo.getPlanId());
        lqw.eq(StringUtils.isNotBlank(bo.getTaskId()), WpmProPlanChange::getTaskId, bo.getTaskId());
        lqw.like(StringUtils.isNotBlank(bo.getTaskName()), WpmProPlanChange::getTaskName, bo.getTaskName());
        lqw.eq(StringUtils.isNotBlank(bo.getChangeColumn()), WpmProPlanChange::getChangeColumn, bo.getChangeColumn());
        lqw.eq(StringUtils.isNotBlank(bo.getChangeBefore()), WpmProPlanChange::getChangeBefore, bo.getChangeBefore());
        lqw.eq(StringUtils.isNotBlank(bo.getChangeAfter()), WpmProPlanChange::getChangeAfter, bo.getChangeAfter());
        return lqw;
    }

    /**
     * 新增项目计划变更
     *
     * @param bo 项目计划变更
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WpmProPlanChangeBo bo) {
        WpmProPlanChange add = MapstructUtils.convert(bo, WpmProPlanChange.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改项目计划变更
     *
     * @param bo 项目计划变更
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WpmProPlanChangeBo bo) {
        WpmProPlanChange update = MapstructUtils.convert(bo, WpmProPlanChange.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WpmProPlanChange entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除项目计划变更信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public List<WpmProPlanChangeVo> getByLambda(LambdaQueryWrapper<WpmProPlanChange> eq) {
        return baseMapper.selectVoList(eq);
    }

    @Override
    public List<WpmProPlanChangeVo> queryListByProId(MonthReportBo bo) {
        List<WpmProPlanChangeVo> list = baseMapper.selectVoList(new LambdaQueryWrapper<WpmProPlanChange>().
            eq(WpmProPlanChange::getProId, bo.getProId())
            .between(WpmProPlanChange::getCreateTime, bo.getDateStart(), bo.getDateEnd()));
        if (list != null) {
            list.forEach(item -> {
                if ("外部成员".equals(item.getChangeColumn()) || "负责人".equals(item.getChangeColumn())) {
                    if (item.getChangeAfter() != null) {
                        String[] split = item.getChangeAfter().split(",");
                        String str = "";
                        for (String string : split) {
                            if (StringUtils.isNotBlank(str)) {
                                str = str.concat(",").concat(userService.selectUserByEmpId(string));
                            } else {
                                str = userService.selectUserByEmpId(string);
                            }
                            item.setChangeAfter(str);
                        }
                    }
                    if (item.getChangeBefore() != null) {
                        String str = "";
                        String[] split = item.getChangeBefore().split(",");
                        for (String string : split) {
                            if (StringUtils.isNotBlank(string))
                                if (StringUtils.isNotBlank(str)) {
                                    str = str.concat(",").concat(userService.selectUserByEmpId(string));
                                } else {
                                    str = userService.selectUserByEmpId(string);
                                }
                            item.setChangeBefore(str);
                        }
                    }
                }
            });
        }
        return list;
    }
}
