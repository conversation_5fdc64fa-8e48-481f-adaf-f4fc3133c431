package com.cyth.business.plan_history.domain.vo;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cyth.business.plan_history.domain.WpmProPlanHistory;
import com.cyth.common.excel.annotation.ExcelDictFormat;
import com.cyth.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 项目计划历史视图对象 wpm_pro_plan_history
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WpmProPlanHistory.class)
public class WpmProPlanHistoryVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private String proId;

    /**
     * 任务id
     */
    @ExcelProperty(value = "任务id")
    private String taskId;

    private String status;

}
