package com.cyth.business.emp.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.cyth.business.emp.domain.HREMP;
import com.cyth.business.emp.domain.bo.HREMPBo;
import com.cyth.business.emp.domain.vo.HREMPVo;
import com.cyth.business.emp.mapper.HREMPMapper;
import com.cyth.business.emp.service.IHREMPService;
import com.cyth.common.core.utils.MapstructUtils;
import com.cyth.common.core.utils.StringUtils;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 员工Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
@RequiredArgsConstructor
@Service
@DS("sqlserver")
public class HREMPServiceImpl implements IHREMPService {

    private final HREMPMapper baseMapper;

    @Override
    @DS("sqlserver")
    public List<HREMPVo> queryByProjectId(String projectId) {
        return baseMapper.getList(projectId);
    }

    @Override
    public List<HREMPVo> querByEmpId(String pmprojectManager, String gcs) {
        return baseMapper.getManagerList(pmprojectManager, gcs);
    }
}
