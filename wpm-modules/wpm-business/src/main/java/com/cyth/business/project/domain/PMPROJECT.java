package com.cyth.business.project.domain;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cyth.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.io.Serial;

/**
 * 项目对象 PMPROJECT
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
@Data

@TableName("PMPROJECT")
public class PMPROJECT implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "PMPROJECT_PRJID")
    private String pmprojectPrjid;

    /**
     * $column.columnComment
     */
    private Date pmprojectCreatedt;

    /**
     * $column.columnComment
     */
    private String pmprojectCreateby;

    /**
     * $column.columnComment
     */
    private String pmprojectCode;

    /**
     * $column.columnComment
     */
    private String pmprojectName;

    /**
     * $column.columnComment
     */
    private Long pmprojectStatus;

    //PMPROJECT_MANAGER  项目经理
    private String pmprojectManager;

    //PMPROJECT_PLANSDATE 计划开工日期
    private Date pmprojectPlansdate;

    //PMPROJECT_FINISHDATE  计划完工日期
    private Date pmprojectFinishdate;

    //PMPROJECT_MUSTFDATE 实际完工日期
    private Date pmprojectMustfdate;


}
