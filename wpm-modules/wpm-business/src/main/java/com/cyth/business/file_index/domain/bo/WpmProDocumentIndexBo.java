package com.cyth.business.file_index.domain.bo;


import com.cyth.business.file_index.domain.WpmProDocumentIndex;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 项目资料目录业务对象 wpm_pro_document_index
 *
 * <AUTHOR>
 * @date 2024-10-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WpmProDocumentIndex.class, reverseConvertGenerate = false)
public class WpmProDocumentIndexBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 资料名称
     */
    @NotBlank(message = "资料名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;
    private String fileType;

    private String[] fileTypes;

    /**
     * 序号
     */
    @NotBlank(message = "序号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String code;

    /**
     * 排序号
     */
    @NotNull(message = "排序号不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderNum;

    /**
     * 父级目录id
     */
    @NotNull(message = "父级目录id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long parentId;

    /**
     * 是否删除
     */
    //@NotNull(message = "是否删除不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer isDelete;

    /**
     * 是否文件
     */
    private Long isFile;

}
