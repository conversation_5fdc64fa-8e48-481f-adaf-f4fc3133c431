package com.cyth.business.emp.domain.vo;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.cyth.business.emp.domain.HREMP;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cyth.common.excel.annotation.ExcelDictFormat;
import com.cyth.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 员工视图对象 HREMP
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = HREMP.class)
public class HREMPVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * hrempEmpid
     */
    private String hrempEmpid;

    /**
     * PMWBSRESEST_PRJID
     */
    private String projectId;

    /**
     * HREMP_EMAIL
     */

    private String email;

    /**
     * HREMP_NAME
     */
    private String hrempName;
    //HREMP_PHONEC
    private String hrempPhonec;

    //HREMP_EMPCODE
    private String hrempEmpcode;

    //HREMP_ORG
    private String hrempOrg;

    /**
     * HREMP_STATUS
     */
    private String  hrempStatus;

}
