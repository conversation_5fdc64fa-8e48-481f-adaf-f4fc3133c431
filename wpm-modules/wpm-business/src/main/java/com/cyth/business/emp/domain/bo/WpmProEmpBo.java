package com.cyth.business.emp.domain.bo;


import com.cyth.business.emp.domain.WpmProEmp;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 项目雇员业务对象 wpm_pro_emp
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WpmProEmp.class, reverseConvertGenerate = false)
public class WpmProEmpBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 项目id
     */
    @NotBlank(message = "项目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String proId;

    /**
     * 雇员id
     */
    //@NotBlank(message = "雇员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String empId;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String empName;

    /**
     * 类型
     */
    //@NotBlank(message = "类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String empType;

    /**
     * 状态
     */
    //@NotBlank(message = "状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String empStatus;

    /**
     * 电话
     */
    //@NotBlank(message = "电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String empPhone;

    private String userName;
    @Size(min = 1, message = "用户角色不能为空")
    private Long [] roleIds;

    private String remark;
}
