package com.cyth.business.plan.domain;

import com.cyth.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 项目计划对象 wpm_pro_plan
 *
 * <AUTHOR>
 * @date 2024-09-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wpm_pro_plan")
public class WpmProPlan extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 项目ID
     */
    private String proId;

    /**
     * 主任务ID
     */
    private Long taskId;

    /**
     * 主任务名称
     */
    private String taskName;

    private Long parentTaskId;

    /**
     * 负责人ID
     */
    private String chargeUserId;

    /**
     * 外部负责人ID
     */
    private String externalUserId;

    /**
     * 计划开工时间
     */
    private Date planStartTime;

    /**
     * 实际开工时间
     */
    private Date realStartTime;

    /**
     * 计划完工时间
     */
    private Date planEndTime;

    /**
     * 实际完工时间
     */
    private Date realEndTime;

    /**
     * 是否删除
     */
    private Integer isDelete;

    private Integer status;

    /**
     * 开工状态
     */
    private Integer startStatus;
    /**
     * 结束状态
     */
    private Integer endStatus;

}
