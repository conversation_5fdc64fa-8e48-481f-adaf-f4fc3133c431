package com.cyth.business.pro_pay.service;


import com.cyth.business.plan_change.domain.bo.MonthReportBo;
import com.cyth.business.pro_pay.domain.bo.WpmProPaymentInfoBo;
import com.cyth.business.pro_pay.domain.vo.PaymentInfoMonthVo;
import com.cyth.business.pro_pay.domain.vo.WpmProPaymentInfoVo;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 项目付款信息Service接口
 *
 * <AUTHOR>
 * @date 2024-11-08
 */
public interface IWpmProPaymentInfoService {

    /**
     * 查询项目付款信息
     *
     * @param id 主键
     * @return 项目付款信息
     */
    WpmProPaymentInfoVo queryById(Long id);

    /**
     * 分页查询项目付款信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目付款信息分页列表
     */
    TableDataInfo<WpmProPaymentInfoVo> queryPageList(WpmProPaymentInfoBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的项目付款信息列表
     *
     * @param bo 查询条件
     * @return 项目付款信息列表
     */
    List<WpmProPaymentInfoVo> queryList(WpmProPaymentInfoBo bo);

    /**
     * 新增项目付款信息
     *
     * @param bo 项目付款信息
     * @return 是否新增成功
     */
    Boolean insertByBo(WpmProPaymentInfoBo bo);

    /**
     * 修改项目付款信息
     *
     * @param bo 项目付款信息
     * @return 是否修改成功
     */
    Boolean updateByBo(WpmProPaymentInfoBo bo);

    /**
     * 校验并批量删除项目付款信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    PaymentInfoMonthVo queryPaymentInfoMonth(MonthReportBo bo);
}
