package com.cyth.business.file_upload.domain.bo;


import com.cyth.business.file_upload.domain.WpmProFilesUpload;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 项目资料上传业务对象 wpm_pro_files_upload
 *
 * <AUTHOR>
 * @date 2024-10-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WpmProFilesUpload.class, reverseConvertGenerate = false)
public class WpmProFilesUploadBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 项目id
     */
    @NotBlank(message = "项目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String proId;

    /**
     * 文件名称
     */
    @NotBlank(message = "目录名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String indexName;
    //@NotBlank(message = "文件名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileName;
    /**
     * oss文件id
     */
    @NotBlank(message = "oss文件id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ossIds;

    /**
     * 状态
     */
    //@NotBlank(message = "状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 任务id
     */
    //@NotBlank(message = "任务id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String taskId;
    @NotNull(message = "项目文件id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long proFileId;
}
