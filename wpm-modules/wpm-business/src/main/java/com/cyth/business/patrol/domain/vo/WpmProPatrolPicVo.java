package com.cyth.business.patrol.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cyth.business.patrol.domain.WpmProPatrolPic;
import com.cyth.common.excel.annotation.ExcelDictFormat;
import com.cyth.common.excel.convert.ExcelDictConvert;
import com.cyth.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 巡检图片视图对象 wpm_pro_patrol_pic
 *
 * <AUTHOR>
 * @date 2024-10-10
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WpmProPatrolPic.class)
public class WpmProPatrolPicVo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 项目ID
     */
    @ExcelProperty(value = "项目ID")
    private String proId;

    /**
     * 巡检计划任务id
     */
    @ExcelProperty(value = "巡检计划任务id")
    private Long planTaskId;

    /**
     * 图片ossId
     */
    @ExcelProperty(value = "图片ossId")
    private String ossIds;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private Long status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 巡检类型
     */
    @ExcelProperty(value = "巡检类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "xuncha_type")
    private String xunType;
    private String urls;

    private String auditStatus;

    private String taskId;

    private String rectify;
}
