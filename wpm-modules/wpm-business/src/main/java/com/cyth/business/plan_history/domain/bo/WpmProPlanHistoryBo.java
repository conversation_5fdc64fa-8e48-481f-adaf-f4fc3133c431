package com.cyth.business.plan_history.domain.bo;


import com.cyth.business.plan_history.domain.WpmProPlanHistory;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 项目计划历史业务对象 wpm_pro_plan_history
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WpmProPlanHistory.class, reverseConvertGenerate = false)
public class WpmProPlanHistoryBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 项目id
     */
    @NotBlank(message = "项目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String proId;

    /**
     * 任务id
     */
    //@NotBlank(message = "任务id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String taskId;

    private String status;
}
