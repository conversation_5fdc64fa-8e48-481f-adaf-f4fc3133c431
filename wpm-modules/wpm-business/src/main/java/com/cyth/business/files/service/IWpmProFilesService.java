package com.cyth.business.files.service;


import com.cyth.business.files.domain.bo.WpmProFilesBo;
import com.cyth.business.files.domain.vo.WpmProFilesVo;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.cyth.workflow.domain.bo.StartProcessBo;

import java.io.IOException;
import java.util.Collection;
import java.util.List;

/**
 * 项目资料Service接口
 *
 * <AUTHOR>
 * @date 2024-09-23
 */
public interface IWpmProFilesService {

    /**
     * 查询项目资料
     *
     * @param id 主键
     * @return 项目资料
     */
    WpmProFilesVo queryById(Long id);

    /**
     * 分页查询项目资料列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目资料分页列表
     */
    TableDataInfo<WpmProFilesVo> queryPageList(WpmProFilesBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的项目资料列表
     *
     * @param bo 查询条件
     * @return 项目资料列表
     */
    List<WpmProFilesVo> queryList(WpmProFilesBo bo);

    /**
     * 新增项目资料
     *
     * @param bo 项目资料
     * @return 是否新增成功
     */
    Boolean insertByBo(WpmProFilesBo bo);

    /**
     * 修改项目资料
     *
     * @param bo 项目资料
     * @return 是否修改成功
     */
    Boolean updateByBo(WpmProFilesBo bo);

    /**
     * 校验并批量删除项目资料信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    List<WpmProFilesBo> init();

    void updateIndex(WpmProFilesBo bo);

    void updateFileType(WpmProFilesBo bo);

    int updateFileOssid(WpmProFilesBo bo);

    Boolean handleDelete(List<Long> ids);

    int updateRemark(WpmProFilesBo bo);

    byte[] batchDownload(List<Long> ids) throws IOException;

    void updatePreFileIsGet(Long proFileId);
}
