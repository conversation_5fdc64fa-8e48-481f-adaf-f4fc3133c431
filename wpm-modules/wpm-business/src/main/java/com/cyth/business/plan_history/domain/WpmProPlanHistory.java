package com.cyth.business.plan_history.domain;

import com.cyth.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 项目计划历史对象 wpm_pro_plan_history
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wpm_pro_plan_history")
public class WpmProPlanHistory extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 项目id
     */
    private String proId;

    /**
     * 任务id
     */
    private String taskId;
    private String status;

}
