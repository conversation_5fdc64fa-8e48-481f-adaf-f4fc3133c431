package com.cyth.business.emp.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.cyth.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 项目雇员对象 wpm_pro_emp
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wpm_pro_emp")
public class WpmProEmp extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id",type =IdType.ASSIGN_ID )
    private Long id;

    /**
     * 项目id
     */
    private String proId;

    /**
     * 雇员id
     */
    private String empId;

    /**
     * 姓名
     */
    private String empName;

    /**
     * 类型
     */
    private String empType;

    /**
     * 状态
     */
    private String empStatus;

    /**
     * 电话
     */
    private String empPhone;

    private String remark;


}
