package com.cyth.business.patrol.domain.bo;

import com.cyth.business.patrol.domain.WpmProPatrolPic;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 巡检图片业务对象 wpm_pro_patrol_pic
 *
 * <AUTHOR>
 * @date 2024-10-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WpmProPatrolPic.class, reverseConvertGenerate = false)
public class WpmProPatrolPicBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 项目ID
     */
    @NotBlank(message = "项目ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String proId;

    /**
     * 巡检计划任务id
     */
    @NotNull(message = "巡检计划任务id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long planTaskId;

    /**
     * 图片ossId
     */
    @NotBlank(message = "图片ossId不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ossIds;

    /**
     * 状态
     */
    @NotNull(message = "状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long status;

    /**
     * 备注
     */
    //@NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;

    /**
     * 巡检类型
     */
    @NotBlank(message = "巡检类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String xunType;

    private String auditStatus;

    private String taskId;

    private String rectify;
}
