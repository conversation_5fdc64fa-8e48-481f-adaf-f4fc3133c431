package com.cyth.business.annotation.impl;


import com.cyth.business.annotation.CheckTaskStatus;
import com.cyth.business.annotation.OnlyCreatorCanDelete;
import com.cyth.common.core.utils.SpringUtils;
import com.cyth.common.mybatis.core.domain.BaseEntity;
import com.cyth.common.satoken.utils.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;

import javax.sql.rowset.serial.SerialException;
import java.io.Serializable;
import java.lang.reflect.Parameter;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 版本号
 * @date 2024年10月18日 11:22
 * @description
 */
@Aspect
@Slf4j
public class CreatorCheckAspect {
    @Around("@annotation(onlyCreatorCanDelete)")
    public Object checkCreator(ProceedingJoinPoint joinPoint, OnlyCreatorCanDelete onlyCreatorCanDelete) throws Throwable {
        log.info("OnlyCreatorCanDelete 执行了删除操作，开始检查创建者");
        // 获取当前登录用户
        Long currentUser = LoginHelper.getUserId();
        // 获取方法签名和参数
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Parameter[] parameters = signature.getMethod().getParameters();
        Long[] ids = null;
        for (int i = 0; i < parameters.length; i++) {
            if (parameters[i].getType().equals(Long[].class)) {
                ids = (Long[]) joinPoint.getArgs()[i];
                break;
            }
        }
        if (ids == null ) {
            throw new IllegalArgumentException(" IDs are required.");
        }

        // 将字符串转换为Long列表
        List<Long> itemIds = Arrays.stream(ids).toList();

        // 获取注解中的实体类类型
        Class<?> entityClass = onlyCreatorCanDelete.value();
        String simpleName = entityClass.getSimpleName();
        String beanName = Character.toLowerCase(simpleName.charAt(0)) + simpleName.substring(1) + "Mapper";
        Object bean = SpringUtils.getBean(beanName);
        //Arrays.stream(bean.getClass().getDeclaredMethods()).forEach(System.out::println);
        // 根据实体类类型进行权限检查
        for (Long itemId : itemIds) {
            Object entity = bean.getClass().getMethod("selectById", Serializable.class).invoke(bean, itemId);
            if (entity == null) {
                throw new SerialException("参数错误");
            }
            //entity 转化成entityClass 并且获取createBy跟currentUser比较
            if (!currentUser.equals(((BaseEntity) entity).getCreateBy())) {
                throw new SerialException("无权限操作");
            }
        }
        // 继续执行被拦截的方法
        return joinPoint.proceed();
    }

    @AfterThrowing(value = "@annotation(canDelete)", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, OnlyCreatorCanDelete canDelete, Exception e) {
        if (e !=null){
            log.error("checkTaskStatus run error",e);

        }
    }
}
