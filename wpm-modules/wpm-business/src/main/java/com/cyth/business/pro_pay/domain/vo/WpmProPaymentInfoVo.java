package com.cyth.business.pro_pay.domain.vo;

import java.util.Date;

import com.cyth.business.pro_pay.domain.WpmProPaymentInfo;
import com.cyth.common.json.anno.PriceFormatter;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cyth.common.excel.annotation.ExcelDictFormat;
import com.cyth.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 项目付款信息视图对象 wpm_pro_payment_info
 *
 * <AUTHOR>
 * @date 2024-11-08
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WpmProPaymentInfo.class)
public class WpmProPaymentInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 付款类型
     */
    @ExcelProperty(value = "付款类型")
    private String payType;

    /**
     * 付款金额
     */
    @ExcelProperty(value = "付款金额")
    @PriceFormatter
    private String payAmount;

    /**
     * 付款日期
     */
    @ExcelProperty(value = "付款日期")
    private Date payDate;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private String proId;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
