package com.cyth.business.pro_pay.domain.vo;

import com.cyth.common.json.anno.PriceFormatter;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 版本号
 * @date 2024年11月13日 16:54
 * @description
 */
@Data
public class PaymentInfoMonthVo implements Serializable {
    //total_investment总投资金额
    //@PriceFormatter(pattern = "0.0000")
    private String totalInvestment;

    //control_price控制价
    //@PriceFormatter(pattern = "0.0000")
    private String controlPrice;
    //contract_price合同价
    //@PriceFormatter(pattern = "0.0000")
    private String contractPrice;
    //预付款
    //@PriceFormatter(pattern = "0.0000")
    private String prePayment;
    //付款记录
    private Set<String> paymentRecord;
    //已付款
    //@PriceFormatter(pattern = "0.0000")
    private String paid;
    //支付进度
    private String paymentProgress;
}
