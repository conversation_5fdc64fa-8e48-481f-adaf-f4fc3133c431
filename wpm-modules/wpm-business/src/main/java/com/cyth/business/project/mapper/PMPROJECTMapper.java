package com.cyth.business.project.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cyth.business.project.domain.PMPROJECT;
import com.cyth.business.project.domain.vo.PMPROJECTVo;
import com.cyth.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 项目Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
@InterceptorIgnore(tenantLine = "true", dataPermission = "false")
public interface PMPROJECTMapper extends BaseMapperPlus<PMPROJECT, PMPROJECTVo> {
//    @InterceptorIgnore(tenantLine = "true", dataPermission = "false")
//    @DS("sqlserver")
Page<PMPROJECTVo> selectByCondition(Page<PMPROJECTVo> page, PMPROJECTVo condition);
}
