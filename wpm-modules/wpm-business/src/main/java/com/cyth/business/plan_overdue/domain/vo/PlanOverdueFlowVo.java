package com.cyth.business.plan_overdue.domain.vo;

import cn.dev33.satoken.error.SaErrorCode;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 版本号
 * @date 2024年10月30日 15:12
 * @description
 */
@Data
public class PlanOverdueFlowVo implements Serializable {
    //wppo.reason, wppo.remark, wppo.handle_emp_id, wp.charge_user_id, wp.task_name, w.pro_name
    private String reason;
    private String remark;
    private String handleEmpId;
    private String chargeUserId;
    private String chargeUser;
    private String taskName;
    private String proName;
    //pro_manager_id
    private String proManagerId;

    private String proManager;

    private String status;
    private String taskId;

}
