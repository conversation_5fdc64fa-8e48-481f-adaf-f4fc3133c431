package com.cyth.business.project.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cyth.business.project.domain.vo.PMPROJECTVo;
import com.cyth.business.project.mapper.PMPROJECTMapper;
import com.cyth.business.project.service.IPMPROJECTService;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.tenant.helper.TenantHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 项目Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
@RequiredArgsConstructor
@Service
@DS("sqlserver")
public class PMPROJECTServiceImpl implements IPMPROJECTService {

    private final PMPROJECTMapper baseMapper;


    @Override
    public TableDataInfo<PMPROJECTVo> queryList(PageQuery pageQuery, PMPROJECTVo pmprojectVo) {
        Page<PMPROJECTVo> page = baseMapper.selectByCondition(pageQuery.build(), pmprojectVo);
        return TableDataInfo.build(page);
    }
}
