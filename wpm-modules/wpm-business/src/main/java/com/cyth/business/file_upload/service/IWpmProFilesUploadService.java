package com.cyth.business.file_upload.service;


import com.cyth.business.file_upload.domain.bo.WpmProFilesUploadBo;
import com.cyth.business.file_upload.domain.vo.WpmProFilesUploadVo;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;

import java.io.IOException;
import java.util.Collection;
import java.util.List;

/**
 * 项目资料上传Service接口
 *
 * <AUTHOR>
 * @date 2024-10-16
 */
public interface IWpmProFilesUploadService {

    /**
     * 查询项目资料上传
     *
     * @param id 主键
     * @return 项目资料上传
     */
    WpmProFilesUploadVo queryById(Long id);

    /**
     * 分页查询项目资料上传列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目资料上传分页列表
     */
    TableDataInfo<WpmProFilesUploadVo> queryPageList(WpmProFilesUploadBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的项目资料上传列表
     *
     * @param bo 查询条件
     * @return 项目资料上传列表
     */
    List<WpmProFilesUploadVo> queryList(WpmProFilesUploadBo bo);

    /**
     * 新增项目资料上传
     *
     * @param bo 项目资料上传
     * @return 是否新增成功
     */
    Boolean insertByBo(WpmProFilesUploadBo bo);

    /**
     * 修改项目资料上传
     *
     * @param bo 项目资料上传
     * @return 是否修改成功
     */
    Boolean updateByBo(WpmProFilesUploadBo bo);

    /**
     * 校验并批量删除项目资料上传信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    byte[] batchDownload(List<Long> ids) throws IOException;
}
