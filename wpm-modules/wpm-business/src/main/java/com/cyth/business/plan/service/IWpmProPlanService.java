package com.cyth.business.plan.service;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cyth.business.plan.domain.WpmProPlan;
import com.cyth.business.plan.domain.bo.WpmProPlanBo;
import com.cyth.business.plan.domain.vo.StatisticsPlanVo;
import com.cyth.business.plan.domain.vo.WpmProPlanVo;
import com.cyth.business.plan_change.domain.bo.MonthReportBo;
import com.cyth.business.tasks.domain.bo.WpmPlanTasksBo;
import com.cyth.business.tasks.domain.vo.TaskTreeVo;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 项目计划Service接口
 *
 * <AUTHOR>
 * @date 2024-09-18
 */
public interface IWpmProPlanService {

    /**
     * 查询项目计划
     *
     * @param id 主键
     * @return 项目计划
     */
    WpmProPlanVo queryById(Long id);

    /**
     * 分页查询项目计划列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目计划分页列表
     */
    TableDataInfo<WpmProPlanVo> queryPageList(WpmProPlanBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的项目计划列表
     *
     * @param bo 查询条件
     * @return 项目计划列表
     */
    List<WpmProPlanVo> queryList(WpmProPlanBo bo);

    /**
     * 新增项目计划
     *
     * @param bo 项目计划
     * @return 是否新增成功
     */
    Boolean insertByBo(WpmProPlanBo bo);

    /**
     * 修改项目计划
     *
     * @param bo 项目计划
     * @return 是否修改成功
     */
    Boolean updateByBo(WpmProPlanBo bo);

    /**
     * 校验并批量删除项目计划信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Boolean savePlan(WpmProPlanBo bo);

    Boolean changeEndTime(WpmProPlanBo bo);

    Boolean changeStartTime(WpmProPlanBo bo);

    Boolean changeChargePerson(WpmProPlanBo bo);

    Boolean changeExternalUserId(WpmProPlanBo bo);

    int startPlan(WpmProPlanBo bo);

    int complatePlan(WpmProPlanBo bo);

    List<WpmProPlanVo> currentManage(WpmProPlanBo bo);

    int enable(List<Long> ids, boolean b);

    void updateLambda(LambdaUpdateWrapper<WpmProPlan> set);

    TaskTreeVo initTreeSelect(WpmProPlanBo tasksBo);

    List<StatisticsPlanVo> queryStatisticsPlan(String proId);

    List<WpmProPlanVo> getByPlanEndTime(int i);

    List<WpmProPlanVo> monthReport(MonthReportBo bo);

    List<WpmProPlanVo> waitingTask(MonthReportBo bo);
}
