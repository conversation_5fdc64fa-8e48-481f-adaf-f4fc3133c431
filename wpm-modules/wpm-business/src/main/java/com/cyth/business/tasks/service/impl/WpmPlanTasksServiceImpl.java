package com.cyth.business.tasks.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import com.cyth.business.tasks.domain.WpmPlanTasks;
import com.cyth.business.tasks.domain.bo.WpmPlanTasksBo;
import com.cyth.business.tasks.domain.vo.WpmPlanTasksVo;
import com.cyth.business.tasks.mapper.WpmPlanTasksMapper;
import com.cyth.business.tasks.service.IWpmPlanTasksService;
import com.cyth.common.core.utils.MapstructUtils;
import com.cyth.common.core.utils.StringUtils;
import com.cyth.common.core.utils.TreeBuildUtils;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-18
 */
@RequiredArgsConstructor
@Service
public class WpmPlanTasksServiceImpl implements IWpmPlanTasksService {

    private final WpmPlanTasksMapper baseMapper;

    /**
     * 查询任务
     *
     * @param id 主键
     * @return 任务
     */
    @Override
    public WpmPlanTasksVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询任务列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 任务分页列表
     */
    @Override
    public TableDataInfo<WpmPlanTasksVo> queryPageList(WpmPlanTasksBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WpmPlanTasks> lqw = buildQueryWrapper(bo);
        Page<WpmPlanTasksVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的任务列表
     *
     * @param bo 查询条件
     * @return 任务列表
     */
    @Override
    public List<WpmPlanTasksVo> queryList(WpmPlanTasksBo bo) {
        LambdaQueryWrapper<WpmPlanTasks> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WpmPlanTasks> buildQueryWrapper(WpmPlanTasksBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WpmPlanTasks> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getTaskName()), WpmPlanTasks::getTaskName, bo.getTaskName());
        lqw.eq(bo.getParentId() != null, WpmPlanTasks::getParentId, bo.getParentId());
        lqw.eq(bo.getTaskStatus() != null, WpmPlanTasks::getTaskStatus, bo.getTaskStatus());
        return lqw;
    }

    /**
     * 新增任务
     *
     * @param bo 任务
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WpmPlanTasksBo bo) {
        WpmPlanTasks add = MapstructUtils.convert(bo, WpmPlanTasks.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改任务
     *
     * @param bo 任务
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WpmPlanTasksBo bo) {
        WpmPlanTasks update = MapstructUtils.convert(bo, WpmPlanTasks.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WpmPlanTasks entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除任务信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public List<WpmPlanTasksVo> selectTaskList(WpmPlanTasksBo tasksBo) {
        return baseMapper.selectVoList();
    }

    @Override
    public List<Tree<Long>> buildTaskTreeSelect(List<WpmPlanTasksVo> tasksVos) {
        if (CollUtil.isEmpty(tasksVos)) {
            return CollUtil.newArrayList();
        } return TreeBuildUtils.build(tasksVos, (task, tree) ->
            tree.setId(task.getId())
                .setParentId(task.getParentId())
                .setName(task.getTaskName())
               );
    }
}
