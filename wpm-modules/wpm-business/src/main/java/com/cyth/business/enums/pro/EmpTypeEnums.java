package com.cyth.business.enums.pro;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 版本号
 * @date 2024年09月27日 17:11
 * @description
 */
@Getter
@AllArgsConstructor
public enum EmpTypeEnums {
    //inner
    INNER("inner", "内部员工"),
    //landlord
    LANDLORD("landlord", "业主人员"),
    //supervisor
    SUPERVISOR("supervisor", "监理人员"),
    //worker
    WORKER("worker", "施工人员"),
    //interface
    INTERFACE("interface", "客户对接人员"),
    //other
    OTHER("other", "其他人员"),
    ;

 private final String code;
 private final String desc;
}
