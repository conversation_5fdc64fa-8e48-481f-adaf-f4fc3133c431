package com.cyth.business.plan_change.domain.vo;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cyth.business.plan_change.domain.WpmProPlanChange;
import com.cyth.common.excel.annotation.ExcelDictFormat;
import com.cyth.common.excel.convert.ExcelDictConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 项目计划变更视图对象 wpm_pro_plan_change
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WpmProPlanChange.class)
public class WpmProPlanChangeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private String proId;

    /**
     * 计划id
     */
    @ExcelProperty(value = "计划id")
    private Long planId;

    /**
     * 任务id
     */
    @ExcelProperty(value = "任务id")
    private String taskId;

    /**
     * 任务名称
     */
    @ExcelProperty(value = "任务名称")
    private String taskName;

    /**
     * 变更字段
     */
    @ExcelProperty(value = "变更字段")
    private String changeColumn;

    /**
     * 变更前
     */
    @ExcelProperty(value = "变更前")
    private String changeBefore;

    /**
     * 变更后
     */
    @ExcelProperty(value = "变更后")
    private String changeAfter;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
}
