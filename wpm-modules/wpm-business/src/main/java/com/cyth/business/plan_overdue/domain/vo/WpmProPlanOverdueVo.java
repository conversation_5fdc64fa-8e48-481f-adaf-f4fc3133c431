package com.cyth.business.plan_overdue.domain.vo;

import com.cyth.business.plan_overdue.domain.WpmProPlanOverdue;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cyth.common.excel.annotation.ExcelDictFormat;
import com.cyth.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 项目超期视图对象 wpm_pro_plan_overdue
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WpmProPlanOverdue.class)
public class WpmProPlanOverdueVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 项目编号
     */
    @ExcelProperty(value = "项目编号")
    private String proId;

    /**
     * 任务名称
     */
    @ExcelProperty(value = "任务名称")
    private String taskName;

    /**
     * 计划任务编号
     */
    @ExcelProperty(value = "计划任务编号")
    private Long planTaskId;

    /**
     * 处理人
     */
    @ExcelProperty(value = "处理人")
    private Long handleId;

    /**
     * 处理人id
     */
    @ExcelProperty(value = "处理人id")
    private String handleEmpId;



    /**
     * 工作流任务编号
     */
    @ExcelProperty(value = "工作流任务编号")
    private Long taskId;

    /**
     * 公告内容
     */
    @ExcelProperty(value = "公告内容")
    private String reason;

    /**
     * 工作流状态
     */
    @ExcelProperty(value = "工作流状态")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
