package com.cyth.business.project.domain.bo;


import com.cyth.business.project.domain.WpmProjects;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 项目业务对象 wpm_projects
 *
 * <AUTHOR>
 * @date 2024-09-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WpmProjects.class, reverseConvertGenerate = false)
public class WpmProjectsBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 项目id，来之诺明
     */
    @NotNull(message = "项目id，来之诺明不能为空", groups = {AddGroup.class, EditGroup.class})
    private String proId;

    /**
     * 项目编码
     */
    @NotBlank(message = "项目编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String proCode;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String proName;

    /**
     * 部门id
     */
    @NotNull(message = "部门id不能为空", groups = {AddGroup.class, EditGroup.class})
    private String proDeptId;

    /**
     * 部门名称
     */
    @NotBlank(message = "部门名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String proDeptName;

    /**
     * 状态
     */
    @NotNull(message = "状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long proStatus;
    private Long complateStatus;
    private Long taskStatus;
    private String taskId;
    //audit_status
    private String auditStatus;


    /**
     * 项目负责人
     */
    @NotNull(message = "项目负责人不能为空", groups = {AddGroup.class, EditGroup.class})
    private String proManagerId;

    /**
     * 计划开始时间
     */
    @NotNull(message = "计划开始时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date proPlanStartTime;

    /**
     * 实际开始时间
     */
    //@NotNull(message = "实际开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date proRealStartTime;

    /**
     * 必须完成时间
     */
    @NotNull(message = "必须完成时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date proFinishTime;

    /**
     * 实际完成时间
     */
    //@NotNull(message = "实际完成时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date proRealFinishTime;

    /**
     * 客户id
     */
    @NotNull(message = "客户id不能为空", groups = {AddGroup.class, EditGroup.class})
    private String proCustomId;

    /**
     * 客户名称
     */
    @NotBlank(message = "客户名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String proCustomName;

    /**
     * 立项时间
     */
    @NotNull(message = "立项时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date proEsTime;

    /**
     * 负责工程师
     */
    @NotNull(message = "负责工程师不能为空", groups = {AddGroup.class, EditGroup.class})
    private String fzgcs;

    /**
     * 合作类型
     */
    @NotBlank(message = "合作类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String proCoType;

    /**
     * 项目规模
     */
    @NotBlank(message = "项目规模不能为空", groups = {AddGroup.class, EditGroup.class})
    private String proScale;

    /**
     * 项目形式
     */
    @NotBlank(message = "项目形式不能为空", groups = {AddGroup.class, EditGroup.class})
    private String proForm;

    /**
     * 管理要求
     */
    @NotBlank(message = "管理要求不能为空", groups = {AddGroup.class, EditGroup.class})
    private String manageNeeds;

    /**
     * 建设单位
     */
    @NotBlank(message = "建设单位不能为空", groups = {AddGroup.class, EditGroup.class})
    private String constructCompany;

    /**
     * 主要指标
     */
    @NotBlank(message = "主要指标不能为空", groups = {AddGroup.class, EditGroup.class})
    private String mainIndicator;

    /**
     * 备注
     */
    private String remark;

    //total_investment总投资金额
    private String totalInvestment;

    //control_price控制价
    private String controlPrice;
    //contract_price合同价
    private String contractPrice;


}
