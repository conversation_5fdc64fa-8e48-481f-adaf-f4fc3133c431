package com.cyth.business.pro_pay.domain.bo;


import com.cyth.business.pro_pay.domain.WpmProPaymentInfo;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 项目付款信息业务对象 wpm_pro_payment_info
 *
 * <AUTHOR>
 * @date 2024-11-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WpmProPaymentInfo.class, reverseConvertGenerate = false)
public class WpmProPaymentInfoBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 付款类型
     */
    @NotBlank(message = "付款类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String payType;

    /**
     * 付款金额
     */
    @NotBlank(message = "付款金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private String payAmount;

    /**
     * 付款日期
     */
    @NotNull(message = "付款日期不能为空", groups = { AddGroup.class, EditGroup.class })
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date payDate;

    /**
     * 项目id
     */
    @NotBlank(message = "项目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String proId;

    /**
     * 备注
     */
    //@NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
