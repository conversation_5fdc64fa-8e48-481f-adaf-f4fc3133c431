package com.cyth.business.emp.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.cyth.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 员工对象 HREMP
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
@Data
@TableName("HREMP")
public class HREMP implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * hrempEmpid
     */
    @TableId(value = "HREMP_EMPID")
    private String hrempEmpid;

    /**
     * PMWBSRESEST_PRJID
     */
    @TableField(value = "PMWBSRESEST_PRJID")
    private String projectId;

    /**
     * HREMP_EMAIL
     */
    @TableField(value = "HREMP_EMAIL")
    private String email;

    /**
     * HREMP_NAME
     */
    private String hrempName;
    //HREMP_PHONEC
    private String hrempPhonec;

    //HREMP_EMPCODE
    private String hrempEmpcode;

    /**
     * HREMP_STATUS
     */
    private Long hrempStatus;


}
