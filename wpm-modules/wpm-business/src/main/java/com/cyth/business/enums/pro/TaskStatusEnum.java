package com.cyth.business.enums.pro;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 版本号
 * @date 2024年09月20日 15:47
 * @description
 */
@AllArgsConstructor
@Getter
public enum TaskStatusEnum {
    INIT(0, "未计划"),
    RUNNING(1, "计划中"),
    SUCCESS(2, "完成"),
    FAIL(3, "变更中"),
    //提交变更
    SUBMIT_CHANGE(4, "提交变更"),
    ;
    private final int code;     // 状态码
    private final String desc;     // 状态描述
}
