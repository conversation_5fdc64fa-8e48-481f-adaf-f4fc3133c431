package com.cyth.business.tasks.domain.vo;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cyth.business.tasks.domain.WpmPlanTasks;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 任务视图对象 wpm_pla_tasks
 *
 * <AUTHOR>
 * @date 2024-09-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WpmPlanTasks.class)
public class WpmPlanTasksVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 任务名称
     */
    @ExcelProperty(value = "任务名称")
    private String taskName;



    /**
     * 父任务id
     */
    @ExcelProperty(value = "父任务id")
    private Long parentId;

    /**
     * 任务状态
     */
    @ExcelProperty(value = "任务状态")
    private Long taskStatus;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    private Integer orderNum;

}
