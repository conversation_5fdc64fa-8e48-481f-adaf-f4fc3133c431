package com.cyth.business.file_index.domain;

import com.cyth.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 项目资料目录对象 wpm_pro_document_index
 *
 * <AUTHOR>
 * @date 2024-10-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wpm_pro_document_index")
public class WpmProDocumentIndex extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 资料名称
     */
    private String name;

    private String fileType;


    /**
     * 序号
     */
    private String code;

    /**
     * 排序号
     */
    private Long orderNum;

    /**
     * 父级目录id
     */
    private Long parentId;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 是否文件
     */
    private Long isFile;

}
