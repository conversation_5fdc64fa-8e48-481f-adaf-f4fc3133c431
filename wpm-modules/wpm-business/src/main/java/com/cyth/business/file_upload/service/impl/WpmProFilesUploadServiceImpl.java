package com.cyth.business.file_upload.service.impl;

import cn.hutool.core.io.IoUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.cyth.business.file_upload.domain.WpmProFilesUpload;
import com.cyth.business.file_upload.domain.bo.WpmProFilesUploadBo;
import com.cyth.business.file_upload.domain.vo.WpmProFilesUploadVo;
import com.cyth.business.file_upload.mapper.WpmProFilesUploadMapper;
import com.cyth.business.file_upload.service.IWpmProFilesUploadService;
import com.cyth.business.files.domain.WpmProFiles;
import com.cyth.business.files.domain.vo.WpmProFilesVo;
import com.cyth.business.files.service.IWpmProFilesService;
import com.cyth.common.core.domain.event.ProcessEvent;
import com.cyth.common.core.domain.event.ProcessTaskEvent;
import com.cyth.common.core.enums.BusinessStatusEnum;
import com.cyth.common.core.utils.MapstructUtils;
import com.cyth.common.core.utils.StringUtils;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.oss.core.OssClient;
import com.cyth.common.oss.factory.OssFactory;
import com.cyth.system.service.ISysOssService;
import com.cyth.workflow.common.constant.ProcessInstanceKeysConstant;
import com.cyth.workflow.domain.bo.CompleteTaskBo;
import com.cyth.workflow.domain.bo.StartProcessBo;
import com.cyth.workflow.service.IActTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 项目资料上传Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-16
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class WpmProFilesUploadServiceImpl implements IWpmProFilesUploadService {

    private final WpmProFilesUploadMapper baseMapper;

    private final IActTaskService actTaskService;

    private final IWpmProFilesService filesService;

    private final ISysOssService ossService;

    /**
     * 查询项目资料上传
     *
     * @param id 主键
     * @return 项目资料上传
     */
    @Override
    public WpmProFilesUploadVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询项目资料上传列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目资料上传分页列表
     */
    @Override
    public TableDataInfo<WpmProFilesUploadVo> queryPageList(WpmProFilesUploadBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WpmProFilesUpload> lqw = buildQueryWrapper(bo);
        Page<WpmProFilesUploadVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的项目资料上传列表
     *
     * @param bo 查询条件
     * @return 项目资料上传列表
     */
    @Override
    public List<WpmProFilesUploadVo> queryList(WpmProFilesUploadBo bo) {
        LambdaQueryWrapper<WpmProFilesUpload> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WpmProFilesUpload> buildQueryWrapper(WpmProFilesUploadBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WpmProFilesUpload> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getProId()), WpmProFilesUpload::getProId, bo.getProId());
        lqw.eq(bo.getProFileId() != null, WpmProFilesUpload::getProFileId, bo.getProFileId());
        lqw.like(StringUtils.isNotBlank(bo.getIndexName()), WpmProFilesUpload::getIndexName, bo.getIndexName());
        lqw.eq(StringUtils.isNotBlank(bo.getOssIds()), WpmProFilesUpload::getOssIds, bo.getOssIds());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), WpmProFilesUpload::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getTaskId()), WpmProFilesUpload::getTaskId, bo.getTaskId());
        return lqw;
    }

    /**
     * 新增项目资料上传
     *
     * @param bo 项目资料上传
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WpmProFilesUploadBo bo) {
        WpmProFilesUpload add = MapstructUtils.convert(bo, WpmProFilesUpload.class);
        validEntityBeforeSave(add);
        add.setStatus(BusinessStatusEnum.DRAFT.getStatus());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            //启动工作流
            //启动工作流
//            StartProcessBo processBo = new StartProcessBo();
//            processBo.setBusinessKey(bo.getId().toString());
//            processBo.setTableName(ProcessInstanceKeysConstant.PRO_FILE_CHECK);
//            Map<String, Object> res = actTaskService.startWorkFlow(processBo);
//            if (!res.isEmpty()) {
//                CompleteTaskBo completeTaskBo = new CompleteTaskBo();
//                completeTaskBo.setTaskId(res.get("taskId").toString());
//                completeTaskBo.setMessageType(Collections.singletonList("1"));
//                actTaskService.completeTask(completeTaskBo);
//            }
        }
        return flag;
    }

    /**
     * 修改项目资料上传
     *
     * @param bo 项目资料上传
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WpmProFilesUploadBo bo) {
        WpmProFilesUpload update = MapstructUtils.convert(bo, WpmProFilesUpload.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WpmProFilesUpload entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除项目资料上传信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        List<Long> ossIds = new ArrayList<>();
        baseMapper.selectBatchIds(ids).stream()
            .map(WpmProFilesUpload::getOssIds)
            .filter(Objects::nonNull)
            .flatMap(oss -> Arrays.stream(StringUtils.split(oss, ",")))
            .map(Long::parseLong)
            .forEach(ossIds::add);
        int res= baseMapper.deleteByIds(ids);
        if (res>0){
            ossService.deleteWithValidByIds(ossIds, false);
        }
        return res>0;
    }

    /**
     * 总体流程监听(例如: 提交 退回 撤销 终止 作废等)
     * 正常使用只需#processEvent.key=='leave1'
     * 示例为了方便则使用startsWith匹配了全部示例key
     *
     * @param processEvent 参数
     */
    @org.springframework.context.event.EventListener(condition = "#processEvent.key.equals('" + ProcessInstanceKeysConstant.PRO_FILE_CHECK + "')")
    public void processHandler(ProcessEvent processEvent) {
        log.info("processHandler当前任务执行了{}", processEvent.toString());
        WpmProFilesUpload proFiles = baseMapper.selectById(Long.valueOf(processEvent.getBusinessKey()));
        proFiles.setStatus(processEvent.getStatus());
        if (processEvent.isSubmit()) {
            proFiles.setStatus(BusinessStatusEnum.WAITING.getStatus());
        }
        if (proFiles.getStatus().equals(BusinessStatusEnum.FINISH.getStatus())) {
            //proFiles.setIsGet(1);
            filesService.updatePreFileIsGet(proFiles.getProFileId());
        }
        baseMapper.updateById(proFiles);
    }

    /**
     * 执行办理任务监听
     * 示例：也可通过  @EventListener(condition = "#processTaskEvent.key=='leave1'")进行判断
     * 在方法中判断流程节点key
     * if ("xxx".equals(processTaskEvent.getTaskDefinitionKey())) {
     * //执行业务逻辑
     * }
     *
     * @param processTaskEvent 参数
     */
    //@EventListener(condition = "#processTaskEvent.key=='leave1' && #processTaskEvent.taskDefinitionKey=='Activity_14633hx'")
    @EventListener(condition = "#processTaskEvent.key=='" + ProcessInstanceKeysConstant.PRO_FILE_CHECK + "'")
    public void processTaskHandler(ProcessTaskEvent processTaskEvent) {
        log.info("processTaskHandler当前任务执行了{}", processTaskEvent.toString());
        WpmProFilesUpload proFiles = baseMapper.selectById(Long.valueOf(processTaskEvent.getBusinessKey()));
        proFiles.setStatus(BusinessStatusEnum.WAITING.getStatus());
        proFiles.setTaskId(processTaskEvent.getTaskId());
        baseMapper.updateById(proFiles);
    }

    @Override
    public byte[] batchDownload(List<Long> ids) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ZipOutputStream zip = new ZipOutputStream(outputStream);
        for (Long id : ids) {
            List<WpmProFilesUploadVo> list = baseMapper.selectVoList(new LambdaQueryWrapper<WpmProFilesUpload>().eq(WpmProFilesUpload::getProFileId, id));
            list.forEach(filesVo -> {
                    if (filesVo != null) {
                        List<Long> ossIds = Arrays.stream(StringUtils.split(filesVo.getOssIds(), ",")).map(Long::parseLong).toList();
                        ossIds.stream().map(ossService::getById).filter(Objects::nonNull).forEach(ossVo -> {
                            OssClient storage = OssFactory.instance(ossVo.getService());
                            try {
                                zip.putNextEntry(new ZipEntry(filesVo.getFileName() + "_" + ossVo.getFileName()));
                                zip.write(IoUtil.readBytes(storage.getObjectContent(ossVo.getUrl())));
                                zip.closeEntry();
                            } catch (IOException e) {
                                throw new RuntimeException(e);
                            }
                        });
                    }
                }
            );

        }
        zip.close();
        return outputStream.toByteArray();
    }
}
