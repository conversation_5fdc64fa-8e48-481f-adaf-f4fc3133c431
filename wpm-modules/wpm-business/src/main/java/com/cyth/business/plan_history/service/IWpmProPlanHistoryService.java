package com.cyth.business.plan_history.service;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cyth.business.plan.domain.WpmProPlan;
import com.cyth.business.plan_history.domain.WpmProPlanHistory;
import com.cyth.business.plan_history.domain.bo.WpmProPlanHistoryBo;
import com.cyth.business.plan_history.domain.vo.WpmProPlanHistoryVo;
import com.cyth.business.project.domain.vo.WpmProjectsVo;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 项目计划历史Service接口
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
public interface IWpmProPlanHistoryService {

    /**
     * 查询项目计划历史
     *
     * @param id 主键
     * @return 项目计划历史
     */
    WpmProPlanHistoryVo queryById(Long id);

    /**
     * 分页查询项目计划历史列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目计划历史分页列表
     */
    TableDataInfo<WpmProPlanHistoryVo> queryPageList(WpmProPlanHistoryBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的项目计划历史列表
     *
     * @param bo 查询条件
     * @return 项目计划历史列表
     */
    List<WpmProPlanHistoryVo> queryList(WpmProPlanHistoryBo bo);

    /**
     * 新增项目计划历史
     *
     * @param bo 项目计划历史
     * @return 是否新增成功
     */
    Boolean insertByBo(WpmProPlanHistoryBo bo);

    /**
     * 修改项目计划历史
     *
     * @param bo 项目计划历史
     * @return 是否修改成功
     */
    Boolean updateByBo(WpmProPlanHistoryBo bo);

    /**
     * 校验并批量删除项目计划历史信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    WpmProPlanHistoryVo addChange(WpmProjectsVo projectsVo);

    WpmProPlanHistoryVo getByProject(WpmProjectsVo projectsVo);

    WpmProPlanHistoryVo getCurrentByProId(String  proId);

    WpmProPlanHistory selectById(Long aLong);

    void updateById(WpmProPlanHistory history);


    void updateTaskId(Long id, String taskId);
}
