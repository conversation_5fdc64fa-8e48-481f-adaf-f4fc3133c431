package com.cyth.business.plan.domain.vo;

import cn.dev33.satoken.error.SaErrorCode;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 版本号
 * @date 2024年10月25日 18:29
 * @description
 */
@Data
public class StatisticsPlanResultVo  implements Serializable {
    List<StatisticsPlanVo> list;
    /**
     * 最大天数
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date maxDay;
    /**
     * 最小天数
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date minDay;
}
