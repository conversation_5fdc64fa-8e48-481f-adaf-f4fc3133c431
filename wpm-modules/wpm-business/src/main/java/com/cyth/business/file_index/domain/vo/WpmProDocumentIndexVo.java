package com.cyth.business.file_index.domain.vo;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cyth.business.file_index.domain.WpmProDocumentIndex;
import com.cyth.common.excel.annotation.ExcelDictFormat;
import com.cyth.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 项目资料目录视图对象 wpm_pro_document_index
 *
 * <AUTHOR>
 * @date 2024-10-16
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WpmProDocumentIndex.class)
public class WpmProDocumentIndexVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 资料名称
     */
    @ExcelProperty(value = "资料名称")
    private String name;

    private String fileType;

    private String[] fileTypes;

    /**
     * 序号
     */
    @ExcelProperty(value = "序号")
    private String code;

    /**
     * 排序号
     */
    @ExcelProperty(value = "排序号")
    private Long orderNum;

    /**
     * 父级目录id
     */
    @ExcelProperty(value = "父级目录id")
    private Long parentId;

    /**
     * 是否删除
     */
    @ExcelProperty(value = "是否删除")
    private Integer isDelete;

    /**
     * 是否文件
     */
    private Long isFile;

}
