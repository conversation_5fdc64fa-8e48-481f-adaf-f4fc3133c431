package com.cyth.business.plan_overdue.domain;

import com.cyth.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 项目超期对象 wpm_pro_plan_overdue
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wpm_pro_plan_overdue")
public class WpmProPlanOverdue extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 项目编号
     */
    private String proId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 计划任务编号
     */
    private Long planTaskId;

    /**
     * 处理人
     */
    private Long handleId;

    /**
     * 处理人id
     */
    private String handleEmpId;



    /**
     * 工作流任务编号
     */
    private Long taskId;

    /**
     * 公告内容
     */
    private String reason;

    /**
     * 工作流状态
     */
    private String status;

    /**
     * 备注
     */
    private String remark;


}
