package com.cyth.business.plan_history.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cyth.business.enums.pro.TaskStatusEnum;
import com.cyth.business.plan.domain.WpmProPlan;
import com.cyth.business.plan.domain.bo.WpmProPlanBo;
import com.cyth.business.plan.service.IWpmProPlanService;
import com.cyth.business.plan_change.domain.WpmProPlanChange;
import com.cyth.business.plan_change.domain.vo.WpmProPlanChangeVo;
import com.cyth.business.plan_change.service.IWpmProPlanChangeService;
import com.cyth.business.plan_history.domain.WpmProPlanHistory;
import com.cyth.business.plan_history.domain.bo.WpmProPlanHistoryBo;
import com.cyth.business.plan_history.domain.vo.WpmProPlanHistoryVo;
import com.cyth.business.plan_history.mapper.WpmProPlanHistoryMapper;
import com.cyth.business.plan_history.service.IWpmProPlanHistoryService;
import com.cyth.business.project.domain.WpmProjects;
import com.cyth.business.project.domain.vo.WpmProjectsVo;
import com.cyth.business.project.mapper.WpmProjectsMapper;
import com.cyth.common.core.domain.event.ProcessEvent;
import com.cyth.common.core.domain.event.ProcessTaskEvent;
import com.cyth.common.core.enums.BusinessStatusEnum;
import com.cyth.common.core.utils.MapstructUtils;
import com.cyth.common.core.utils.StringUtils;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cyth.workflow.common.constant.ProcessInstanceKeysConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 项目计划历史Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class WpmProPlanHistoryServiceImpl implements IWpmProPlanHistoryService {

    private final WpmProPlanHistoryMapper baseMapper;


    /**
     * 查询项目计划历史
     *
     * @param id 主键
     * @return 项目计划历史
     */
    @Override
    public WpmProPlanHistoryVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询项目计划历史列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目计划历史分页列表
     */
    @Override
    public TableDataInfo<WpmProPlanHistoryVo> queryPageList(WpmProPlanHistoryBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WpmProPlanHistory> lqw = buildQueryWrapper(bo);
        Page<WpmProPlanHistoryVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的项目计划历史列表
     *
     * @param bo 查询条件
     * @return 项目计划历史列表
     */
    @Override
    public List<WpmProPlanHistoryVo> queryList(WpmProPlanHistoryBo bo) {
        LambdaQueryWrapper<WpmProPlanHistory> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WpmProPlanHistory> buildQueryWrapper(WpmProPlanHistoryBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WpmProPlanHistory> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getProId()), WpmProPlanHistory::getProId, bo.getProId());
        lqw.eq(StringUtils.isNotBlank(bo.getTaskId()), WpmProPlanHistory::getTaskId, bo.getTaskId());
        return lqw;
    }

    /**
     * 新增项目计划历史
     *
     * @param bo 项目计划历史
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WpmProPlanHistoryBo bo) {
        WpmProPlanHistory add = MapstructUtils.convert(bo, WpmProPlanHistory.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改项目计划历史
     *
     * @param bo 项目计划历史
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WpmProPlanHistoryBo bo) {
        WpmProPlanHistory update = MapstructUtils.convert(bo, WpmProPlanHistory.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WpmProPlanHistory entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除项目计划历史信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public WpmProPlanHistoryVo addChange(WpmProjectsVo projectsVo) {
        WpmProPlanHistoryVo current = getCurrentByProId(projectsVo.getProId());
        /*if (current != null) {
            throw new RuntimeException("当前项目已有变更计划");
        }*/
        WpmProPlanHistoryBo bo = new WpmProPlanHistoryBo();
        bo.setStatus(BusinessStatusEnum.DRAFT.getStatus());
        bo.setProId(projectsVo.getProId());
        boolean flag = insertByBo(bo);
        if (flag) {
            current = new WpmProPlanHistoryVo();
            current.setId(bo.getId());
            current.setProId(bo.getProId());
            current.setStatus(bo.getStatus());
        }
        return current;
    }

    @Override
    public WpmProPlanHistoryVo getByProject(WpmProjectsVo projectsVo) {
        return getCurrentByProId(projectsVo.getProId());
    }

    @Override
    public WpmProPlanHistoryVo getCurrentByProId(String proId) {

        return baseMapper.selectVoOne(new LambdaUpdateWrapper<>(WpmProPlanHistory.class).eq(WpmProPlanHistory::getProId, proId).eq(WpmProPlanHistory::getStatus, BusinessStatusEnum.DRAFT.getStatus()).orderByDesc(WpmProPlanHistory::getCreateTime));
    }

    @Override
    public WpmProPlanHistory selectById(Long aLong) {
        return baseMapper.selectById(aLong);
    }

    @Override
    public void updateById(WpmProPlanHistory history) {
        baseMapper.updateById(history);
    }

    @Override
    public void updateTaskId(Long id, String taskId) {
        baseMapper.update(null,new LambdaUpdateWrapper<WpmProPlanHistory>().eq(WpmProPlanHistory::getId,id).set(WpmProPlanHistory::getTaskId,taskId));
    }
}
