package com.cyth.business.emp.domain.bo;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.cyth.business.emp.domain.HREMP;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 员工业务对象 HREMP
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
@Data
public class HREMPBo implements Serializable {

    /**
     * hrempEmpid
     */
    @TableId(value = "HREMP_EMPID")
    private String hrempEmpid;

    /**
     * PMWBSRESEST_PRJID
     */
    @TableField(value = "PMWBSRESEST_PRJID")
    private String projectId;

    /**
     * HREMP_EMAIL
     */
    @TableField(value = "HREMP_EMAIL")
    private String email;

    /**
     * HREMP_NAME
     */
    private String hrempName;
    //HREMP_PHONEC
    private String hrempPhonec;

    //HREMP_EMPCODE
    private String hrempEmpcode;

    /**
     * HREMP_STATUS
     */
    private Long hrempStatus;


}
