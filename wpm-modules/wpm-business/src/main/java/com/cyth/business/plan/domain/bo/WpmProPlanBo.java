package com.cyth.business.plan.domain.bo;


import com.cyth.business.plan.domain.WpmProPlan;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 项目计划业务对象 wpm_pro_plan
 *
 * <AUTHOR>
 * @date 2024-09-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WpmProPlan.class, reverseConvertGenerate = false)
public class WpmProPlanBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 项目ID
     */
    @NotBlank(message = "项目ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private String proId;

    /**
     * 主任务ID
     */
    //@NotNull(message = "主任务ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long taskId;

    /**
     * 主任务名称
     */
    //@NotBlank(message = "主任务名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String taskName;

    /**
     * 父任务id
     */
    //@NotNull(message = "子任务ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long parentTaskId;

    /**
     * 负责人ID
     */
    //@NotBlank(message = "负责人ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String chargeUserId;
    private String[] chargeUserIds;
    private String[] externalUserIds;
    /**
     * 外部负责人ID
     */
    //@NotBlank(message = "外部负责人ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String externalUserId;
    /**
     * 计划开工时间
     */
    //@NotNull(message = "计划开工时间不能为空", groups = { AddGroup.class, EditGroup.class })
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date planStartTime;

    /**
     * 实际开工时间
     */
    //@NotNull(message = "实际开工时间不能为空", groups = { AddGroup.class, EditGroup.class })
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date realStartTime;

    /**
     * 计划完工时间
     */
    //@NotNull(message = "计划完工时间不能为空", groups = { AddGroup.class, EditGroup.class })
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date planEndTime;

    /**
     * 实际完工时间
     */
    //@NotNull(message = "实际完工时间不能为空", groups = { AddGroup.class, EditGroup.class })
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date realEndTime;

    /**
     * 是否删除
     */
    //@NotNull(message = "是否删除不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer isDelete;

    private Long[] taskIds;

    private Integer status;

    /**
     * 开工状态
     */
    private Integer startStatus;
    /**
     * 结束状态
     */
    private Integer endStatus;
}
