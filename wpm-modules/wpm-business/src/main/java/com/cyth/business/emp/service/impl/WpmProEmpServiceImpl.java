package com.cyth.business.emp.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.cyth.business.emp.domain.WpmProEmp;
import com.cyth.business.emp.domain.bo.WpmProEmpBo;
import com.cyth.business.emp.domain.vo.HREMPVo;
import com.cyth.business.emp.domain.vo.WpmProEmpVo;
import com.cyth.business.emp.mapper.WpmProEmpMapper;
import com.cyth.business.emp.service.IHREMPService;
import com.cyth.business.emp.service.IWpmProEmpService;
import com.cyth.business.enums.pro.EmpTypeEnums;
import com.cyth.common.core.constant.Constants;
import com.cyth.common.core.utils.MapstructUtils;
import com.cyth.common.core.utils.StringUtils;
import com.cyth.common.core.utils.ValidatorUtils;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cyth.system.domain.SysUser;
import com.cyth.system.domain.bo.SysUserBo;
import com.cyth.system.domain.vo.SysUserVo;
import com.cyth.system.service.ISysUserService;
import com.cyth.system.service.impl.SysUserServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 项目雇员Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@RequiredArgsConstructor
@Service
public class WpmProEmpServiceImpl implements IWpmProEmpService {

    private final WpmProEmpMapper baseMapper;

    private final IHREMPService hrempService;

    private final SysUserServiceImpl sysUserService;

    private final ISysUserService userService;

    private final IdentifierGenerator idGenerator;

    /**
     * 查询项目雇员
     *
     * @param id 主键
     * @return 项目雇员
     */
    @Override
    public WpmProEmpVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询项目雇员列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目雇员分页列表
     */
    @Override
    public TableDataInfo<WpmProEmpVo> queryPageList(WpmProEmpBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WpmProEmp> lqw = buildQueryWrapper(bo);
        Page<WpmProEmpVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的项目雇员列表
     *
     * @param bo 查询条件
     * @return 项目雇员列表
     */
    @Override
    public List<WpmProEmpVo> queryList(WpmProEmpBo bo) {
        LambdaQueryWrapper<WpmProEmp> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WpmProEmp> buildQueryWrapper(WpmProEmpBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WpmProEmp> lqw = Wrappers.lambdaQuery();
        lqw.eq(true, WpmProEmp::getProId, bo.getProId());
        lqw.eq(StringUtils.isNotBlank(bo.getEmpId()), WpmProEmp::getEmpId, bo.getEmpId());
        lqw.like(StringUtils.isNotBlank(bo.getEmpName()), WpmProEmp::getEmpName, bo.getEmpName());
        lqw.eq(StringUtils.isNotBlank(bo.getEmpType()), WpmProEmp::getEmpType, bo.getEmpType());
        lqw.eq(StringUtils.isNotBlank(bo.getEmpStatus()), WpmProEmp::getEmpStatus, bo.getEmpStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getEmpPhone()), WpmProEmp::getEmpPhone, bo.getEmpPhone());
        return lqw;
    }

    /**
     * 新增项目雇员
     *
     * @param bo 项目雇员
     * @return 是否新增成功
     */
    @Override
    @Transactional
    public Boolean insertByBo(WpmProEmpBo bo) {
        WpmProEmp add = MapstructUtils.convert(bo, WpmProEmp.class);
        validEntityBeforeSave(add,bo);
        String uuid = UUID.fastUUID().toString();
        add.setEmpId(uuid);
        add.setEmpStatus(Constants.INTEGER_ZERO.toString());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            SysUserVo sysUserVo = sysUserService.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUserName, bo.getUserName()));
            if (sysUserVo != null) {
                return flag;
            }
            bo.setId(add.getId());
            //增加用户
            SysUserBo sysUserBo = new SysUserBo();
            sysUserBo.setDeptId(1840282109812457474L);
            sysUserBo.setUserName(bo.getUserName());
            sysUserBo.setNickName(bo.getEmpName());
            sysUserBo.setPhonenumber(bo.getEmpPhone());
            sysUserBo.setRoleIds(bo.getRoleIds());
            sysUserBo.setPassword(BCrypt.hashpw("123456"));
            //sysUserBo.setEmail(hrempVo.getEmail());
            sysUserBo.setStatus("0");
            sysUserBo.setEmpId(uuid);
            ValidatorUtils.validate(sysUserBo, AddGroup.class);
            //sysUserService.insert(MapstructUtils.convert(sysUserBo,SysUser.class));
            //角色如果为空 写死 1848194088852684802L  外部成员
            //sysUserBo.setRoleIds(new Long[]{1848194088852684802L});
            userService.insertUser(sysUserBo);
        }
        return flag;
    }

    /**
     * 修改项目雇员
     *
     * @param bo 项目雇员
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WpmProEmpBo bo) {
        WpmProEmp update = MapstructUtils.convert(bo, WpmProEmp.class);
        validEntityBeforeSave(update,null);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WpmProEmp entity,WpmProEmpBo bo) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除项目雇员信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public Boolean syncFromNoma(String proId) {
        if (StringUtils.isNotBlank(proId)) {
            List<HREMPVo> hrempVos = hrempService.queryByProjectId(proId);
            List<WpmProEmpBo> vos = new ArrayList<>();
            List<SysUserBo> sysUserVos = new ArrayList<>();
            if (hrempVos != null && hrempVos.size() > 0) {
                for (HREMPVo hrempVo : hrempVos) {
                    WpmProEmpBo bo = new WpmProEmpBo();
                    bo.setProId(hrempVo.getProjectId());
                    bo.setEmpId(hrempVo.getHrempEmpid());
                    bo.setEmpName(hrempVo.getHrempName());
                    bo.setEmpType(EmpTypeEnums.INNER.getCode());
                    bo.setEmpStatus(Objects.equals(hrempVo.getHrempStatus(), "1") ?"0":"1");
                    bo.setEmpPhone(hrempVo.getHrempPhonec());
                    ValidatorUtils.validate(bo, AddGroup.class);
                    vos.add(bo);
                    SysUserBo sysUserBo = new SysUserBo();
                    sysUserBo.setDeptId(Long.parseLong(hrempVo.getHrempOrg()));
                    sysUserBo.setUserName(hrempVo.getHrempEmpcode());
                    sysUserBo.setNickName(hrempVo.getHrempName());
                    sysUserBo.setPhonenumber(hrempVo.getHrempPhonec());
                    sysUserBo.setEmail(hrempVo.getEmail());
                    sysUserBo.setStatus(Objects.equals(hrempVo.getHrempStatus(), "1") ?"0":"1");
                    sysUserBo.setEmpId(hrempVo.getHrempEmpid());
                    ValidatorUtils.validate(sysUserBo, AddGroup.class);
                    sysUserVos.add(sysUserBo);
                }
            }
            insertOrUpdateBatch(vos, sysUserVos);
        }
        return true;
    }

    @Override
    @Transactional
    public Boolean insertOrUpdateBatch(List<WpmProEmpBo> bos, List<SysUserBo> sysUserbos) {
        for (WpmProEmpBo bo : bos) {
            WpmProEmp wpmProEmp = MapstructUtils.convert(bo, WpmProEmp.class);
            WpmProEmpVo wpmProEmpVo = baseMapper.selectVoOne(new LambdaQueryWrapper<WpmProEmp>()
                .eq(WpmProEmp::getProId, bo.getProId()).eq(WpmProEmp::getEmpId, bo.getEmpId()));
            if (wpmProEmpVo == null) {
                baseMapper.insert(wpmProEmp);
            } else {
                baseMapper.update(wpmProEmp, new LambdaUpdateWrapper<WpmProEmp>().eq(WpmProEmp::getProId, bo.getProId()).eq(WpmProEmp::getEmpId, bo.getEmpId()));
            }
        }
        for (SysUserBo userbo : sysUserbos) {
            SysUser user = MapstructUtils.convert(userbo, SysUser.class);
            user.setPassword(BCrypt.hashpw("123456"));
            SysUserVo sysUserVo = sysUserService.selectVoOne(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getUserName, userbo.getUserName()).eq(SysUser::getEmpId, userbo.getEmpId()));
            if (sysUserVo == null) {
                user.setUserId(idGenerator.nextId(null).longValue());
                sysUserService.insert(user);
                sysUserService.insertUserRole(user.getUserId(), 1915607503577673729L);
            } else
                sysUserService.update(user, new LambdaUpdateWrapper<SysUser>().eq(SysUser::getUserName, userbo.getUserName()).eq(SysUser::getEmpId, userbo.getEmpId()));
        }

        return true;
    }

    @Override
    public List<WpmProEmpVo> getUserByProId(String proId) {
        return baseMapper.selectVoList(new LambdaQueryWrapper<WpmProEmp>().eq(WpmProEmp::getProId, proId));
    }

    @Override
    public int countByProId(String proId) {
        return baseMapper.selectCount(new LambdaQueryWrapper<WpmProEmp>().eq(WpmProEmp::getProId, proId)).intValue();
    }

    @Override
    public List<WpmProEmpVo> selectEmpDeptIdByEmpId(String empId) {
        return baseMapper.selectVoList(new LambdaQueryWrapper<WpmProEmp>().eq(WpmProEmp::getEmpId, empId));
    }

    @Override
    public WpmProEmpVo selectVoOne(LambdaQueryWrapper<WpmProEmp> eq) {
        return baseMapper.selectVoOne(eq);
    }

    @Override
    public void insert(WpmProEmp wpmProEmp) {
        baseMapper.insert(wpmProEmp);
    }

    @Override
    public void update(WpmProEmp wpmProEmp, LambdaUpdateWrapper<WpmProEmp> eq) {
        baseMapper.update(wpmProEmp, eq);
    }

    @Override
    public int updateProEmpStatus(WpmProEmpBo bo) {

        return baseMapper.update(null, new LambdaUpdateWrapper<WpmProEmp>().eq(WpmProEmp::getId, bo.getId()).set(WpmProEmp::getEmpStatus, bo.getEmpStatus()));
    }
}
