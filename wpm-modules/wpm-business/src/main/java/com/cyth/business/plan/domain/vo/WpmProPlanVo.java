package com.cyth.business.plan.domain.vo;

import java.util.Date;

import com.cyth.business.plan.domain.WpmProPlan;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cyth.common.excel.annotation.ExcelDictFormat;
import com.cyth.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 项目计划视图对象 wpm_pro_plan
 *
 * <AUTHOR>
 * @date 2024-09-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WpmProPlan.class)
public class WpmProPlanVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 项目ID
     */
    @ExcelProperty(value = "项目ID")
    private String proId;

    /**
     * 主任务ID
     */
    @ExcelProperty(value = "主任务ID")
    private Long taskId;

    /**
     * 主任务名称
     */
    @ExcelProperty(value = "主任务名称")
    private String taskName;

    private Long parentTaskId;

    /**
     * 负责人ID
     */
    @ExcelProperty(value = "负责人ID")
    private String chargeUserId;

    private String chargeUser;

    /**
     * 外部负责人ID
     */
    @ExcelProperty(value = "外部负责人ID")
    private String externalUserId;

    private String[] chargeUserIds;
    private String[] externalUserIds;

    /**
     * 计划开工时间
     */
    @ExcelProperty(value = "计划开工时间")
    private Date planStartTime;

    /**
     * 实际开工时间
     */
    @ExcelProperty(value = "实际开工时间")
    private Date realStartTime;

    /**
     * 计划完工时间
     */
    @ExcelProperty(value = "计划完工时间")
    private Date planEndTime;

    /**
     * 实际完工时间
     */
    @ExcelProperty(value = "实际完工时间")
    private Date realEndTime;

    /**
     * 是否删除
     */
    @ExcelProperty(value = "是否删除")
    private Integer isDelete;

    private List<WpmProPlanVo> children;

    private Integer status;

    /**
     * 开工状态
     */
    private Integer startStatus;
    /**
     * 结束状态
     */
    private Integer endStatus;
}
