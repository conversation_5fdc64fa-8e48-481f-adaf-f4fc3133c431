package com.cyth.business.tasks.service;


import cn.hutool.core.lang.tree.Tree;
import com.cyth.business.tasks.domain.bo.WpmPlanTasksBo;
import com.cyth.business.tasks.domain.vo.WpmPlanTasksVo;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 任务Service接口
 *
 * <AUTHOR>
 * @date 2024-09-18
 */
public interface IWpmPlanTasksService {

    /**
     * 查询任务
     *
     * @param id 主键
     * @return 任务
     */
    WpmPlanTasksVo queryById(Long id);

    /**
     * 分页查询任务列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 任务分页列表
     */
    TableDataInfo<WpmPlanTasksVo> queryPageList(WpmPlanTasksBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的任务列表
     *
     * @param bo 查询条件
     * @return 任务列表
     */
    List<WpmPlanTasksVo> queryList(WpmPlanTasksBo bo);

    /**
     * 新增任务
     *
     * @param bo 任务
     * @return 是否新增成功
     */
    Boolean insertByBo(WpmPlanTasksBo bo);

    /**
     * 修改任务
     *
     * @param bo 任务
     * @return 是否修改成功
     */
    Boolean updateByBo(WpmPlanTasksBo bo);

    /**
     * 校验并批量删除任务信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    List<WpmPlanTasksVo> selectTaskList(WpmPlanTasksBo tasksBo);

    List<Tree<Long>> buildTaskTreeSelect(List<WpmPlanTasksVo> tasksVos);
}
