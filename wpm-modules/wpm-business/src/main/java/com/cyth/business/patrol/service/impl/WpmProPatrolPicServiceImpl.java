package com.cyth.business.patrol.service.impl;

import com.cyth.business.files.domain.WpmProFiles;
import com.cyth.business.patrol.domain.WpmProPatrolPic;
import com.cyth.business.patrol.domain.bo.WpmProPatrolPicBo;
import com.cyth.business.patrol.domain.vo.WpmProPatrolPicVo;
import com.cyth.business.patrol.domain.vo.WpmProPatrolStatisticsVo;
import com.cyth.business.patrol.mapper.WpmProPatrolPicMapper;
import com.cyth.business.patrol.service.IWpmProPatrolPicService;
import com.cyth.business.plan_change.domain.bo.MonthReportBo;
import com.cyth.common.core.domain.event.ProcessEvent;
import com.cyth.common.core.domain.event.ProcessTaskEvent;
import com.cyth.common.core.enums.BusinessStatusEnum;
import com.cyth.common.core.exception.ServiceException;
import com.cyth.common.core.service.OssService;
import com.cyth.common.core.utils.MapstructUtils;
import com.cyth.common.core.utils.StringUtils;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cyth.common.satoken.utils.LoginHelper;
import com.cyth.common.translation.annotation.Translation;
import com.cyth.system.domain.vo.SysOssVo;
import com.cyth.system.service.ISysOssService;
import com.cyth.workflow.common.constant.ProcessInstanceKeysConstant;
import com.cyth.workflow.domain.bo.CompleteTaskBo;
import com.cyth.workflow.domain.bo.StartProcessBo;
import com.cyth.workflow.service.IActTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.util.*;
import java.util.stream.Collectors;

/**
 * 巡检图片Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-10
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class WpmProPatrolPicServiceImpl implements IWpmProPatrolPicService {

    private final WpmProPatrolPicMapper baseMapper;

    private final OssService ossService;

    private final ISysOssService sysOssService;

    private final IActTaskService actTaskService;

    /**
     * 查询巡检图片
     *
     * @param id 主键
     * @return 巡检图片
     */
    @Override
    public WpmProPatrolPicVo queryById(Long id) {
        WpmProPatrolPicVo pic = baseMapper.selectVoById(id);
        if (pic != null) {
            pic.setUrls(ossService.selectUrlByIds(pic.getOssIds()));
        }
        return pic;
    }

    /**
     * 分页查询巡检图片列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 巡检图片分页列表
     */
    @Override
    public TableDataInfo<WpmProPatrolPicVo> queryPageList(WpmProPatrolPicBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WpmProPatrolPic> lqw = buildQueryWrapper(bo);
        Page<WpmProPatrolPicVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        result.getRecords().forEach(pic -> {
//          List<Long> ossList=  Arrays.stream(pic.getOssIds().split(",")).toList().stream().map(Long::valueOf).toList();
            //List<SysOssVo> list = ossService.selectUrlByIds(pic.getUrls());
            pic.setUrls(ossService.selectUrlByIds(pic.getOssIds()));
        });
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的巡检图片列表
     *
     * @param bo 查询条件
     * @return 巡检图片列表
     */
    @Override
    public List<WpmProPatrolPicVo> queryList(WpmProPatrolPicBo bo) {
        LambdaQueryWrapper<WpmProPatrolPic> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WpmProPatrolPic> buildQueryWrapper(WpmProPatrolPicBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WpmProPatrolPic> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getProId()), WpmProPatrolPic::getProId, bo.getProId());
        lqw.eq(bo.getPlanTaskId() != null, WpmProPatrolPic::getPlanTaskId, bo.getPlanTaskId());
        lqw.eq(StringUtils.isNotBlank(bo.getOssIds()), WpmProPatrolPic::getOssIds, bo.getOssIds());
        return lqw;
    }

    /**
     * 新增巡检图片
     *
     * @param bo 巡检图片
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WpmProPatrolPicBo bo) {
        WpmProPatrolPic add = MapstructUtils.convert(bo, WpmProPatrolPic.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            //不正常启动工作流
            if (bo.getStatus() == 1L) {
                //启动工作流
                StartProcessBo processBo = new StartProcessBo();
                processBo.setBusinessKey(bo.getId().toString());
                processBo.setTableName(ProcessInstanceKeysConstant.XUNCHA_EXCEPTION_CHK);
                Map<String, Object> variables = new HashMap<>();
                variables.put("rectify", bo.getRectify());
                variables.put("checkUser", add.getCreateBy());
                processBo.setVariables(variables);
                Map<String, Object> res = actTaskService.startWorkFlow(processBo);
                if (!res.isEmpty()) {
                    CompleteTaskBo completeTaskBo = new CompleteTaskBo();
                    completeTaskBo.setTaskId(res.get("taskId").toString());
                    completeTaskBo.setMessageType(Collections.singletonList("1"));
                    actTaskService.completeTask(completeTaskBo);
                }
            }
        }
        return flag;
    }

    /**
     * 修改巡检图片
     *
     * @param bo 巡检图片
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WpmProPatrolPicBo bo) {
        WpmProPatrolPic update = MapstructUtils.convert(bo, WpmProPatrolPic.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WpmProPatrolPic entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除巡检图片信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            ids.forEach(id -> {
                WpmProPatrolPic obj = baseMapper.selectById(id);
                if (!Objects.equals(obj.getCreateBy(), LoginHelper.getUserId())) {
                    throw new ServiceException("当前用户没有权限删除此数据");
                }
                if (obj != null) {
                    List<Long> ossIds = Arrays.stream(obj.getOssIds().split(String.valueOf(","))).map(Long::valueOf).toList();
                    sysOssService.deleteWithValidByIds(ossIds, false);
                    baseMapper.deleteById(obj.getId());
                }
            });
        }
        //return baseMapper.deleteByIds(ids) > 0;
        return true;
    }

    @Override
    public List<WpmProPatrolPicVo> getMonthReport(MonthReportBo bo) {

        List<WpmProPatrolPicVo> list =baseMapper.selectVoList(new LambdaQueryWrapper<WpmProPatrolPic>().eq(WpmProPatrolPic::getProId, bo.getProId())
            .between(WpmProPatrolPic::getCreateTime, bo.getDateStart(), bo.getDateEnd()));
        for (WpmProPatrolPicVo picVo : list) {
            if (picVo.getOssIds() != null) {
                picVo.setUrls(ossService.selectUrlByIds(picVo.getOssIds()));
            }
        }
        return list;
    }

    /**
     * 总体流程监听(例如: 提交 退回 撤销 终止 作废等)
     * 正常使用只需#processEvent.key=='leave1'
     * 示例为了方便则使用startsWith匹配了全部示例key
     *
     * @param processEvent 参数
     */
    @org.springframework.context.event.EventListener(condition = "#processEvent.key.equals('" + ProcessInstanceKeysConstant.XUNCHA_EXCEPTION_CHK + "')")
    public void processHandler(ProcessEvent processEvent) {
        log.info("processHandler当前任务执行了{}", processEvent.toString());
        WpmProPatrolPic proFiles = baseMapper.selectById(Long.valueOf(processEvent.getBusinessKey()));
        proFiles.setAuditStatus(processEvent.getStatus());
        if (processEvent.isSubmit()) {
            proFiles.setAuditStatus(BusinessStatusEnum.WAITING.getStatus());
        }
//        if (proFiles.getStatus().equals(BusinessStatusEnum.FINISH.getStatus())) {
//            proFiles.setIsGet(1);
//        }
        baseMapper.updateById(proFiles);
    }

    /**
     * 执行办理任务监听
     * 示例：也可通过  @EventListener(condition = "#processTaskEvent.key=='leave1'")进行判断
     * 在方法中判断流程节点key
     * if ("xxx".equals(processTaskEvent.getTaskDefinitionKey())) {
     * //执行业务逻辑
     * }
     *
     * @param processTaskEvent 参数
     */
    //@EventListener(condition = "#processTaskEvent.key=='leave1' && #processTaskEvent.taskDefinitionKey=='Activity_14633hx'")
    @EventListener(condition = "#processTaskEvent.key=='" + ProcessInstanceKeysConstant.XUNCHA_EXCEPTION_CHK + "'")
    public void processTaskHandler(ProcessTaskEvent processTaskEvent) {
        log.info("processTaskHandler当前任务执行了{}", processTaskEvent.toString());
        WpmProPatrolPic proFiles = baseMapper.selectById(Long.valueOf(processTaskEvent.getBusinessKey()));
        proFiles.setAuditStatus(BusinessStatusEnum.WAITING.getStatus());
        proFiles.setTaskId(processTaskEvent.getTaskId());
        baseMapper.updateById(proFiles);
    }

    @Override
    public List<WpmProPatrolStatisticsVo> getPatrolStatistics(MonthReportBo bo) {
        return baseMapper.selectPatrolStatistics(bo);
    }
}
