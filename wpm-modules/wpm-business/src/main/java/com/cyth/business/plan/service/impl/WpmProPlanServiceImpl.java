package com.cyth.business.plan.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cyth.business.enums.pro.TaskStatusEnum;
import com.cyth.business.plan.domain.WpmProPlan;
import com.cyth.business.plan.domain.bo.WpmProPlanBo;
import com.cyth.business.plan.domain.vo.StatisticsPlanVo;
import com.cyth.business.plan.domain.vo.WpmProPlanVo;
import com.cyth.business.plan.mapper.WpmProPlanMapper;
import com.cyth.business.plan.service.IWpmProPlanService;
import com.cyth.business.plan_change.domain.WpmProPlanChange;
import com.cyth.business.plan_change.domain.bo.MonthReportBo;
import com.cyth.business.plan_change.domain.bo.WpmProPlanChangeBo;
import com.cyth.business.plan_change.domain.vo.WpmProPlanChangeVo;
import com.cyth.business.plan_change.service.IWpmProPlanChangeService;
import com.cyth.business.plan_history.domain.WpmProPlanHistory;
import com.cyth.business.project.domain.WpmProjects;
import com.cyth.business.project.domain.vo.WpmProjectsVo;
import com.cyth.business.project.service.IWpmProjectsService;
import com.cyth.business.tasks.domain.bo.WpmPlanTasksBo;
import com.cyth.business.tasks.domain.vo.TaskTreeVo;
import com.cyth.business.tasks.domain.vo.WpmPlanTasksVo;
import com.cyth.business.tasks.service.IWpmPlanTasksService;
import com.cyth.common.core.constant.Constants;
import com.cyth.common.core.domain.event.ProcessEvent;
import com.cyth.common.core.domain.event.ProcessTaskEvent;
import com.cyth.common.core.enums.BusinessStatusEnum;
import com.cyth.common.core.exception.ServiceException;
import com.cyth.common.core.utils.MapstructUtils;
import com.cyth.common.core.utils.StringUtils;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cyth.system.service.ISysUserService;
import com.cyth.workflow.common.constant.ProcessInstanceKeysConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.stream.Collectors;

/**
 * 项目计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-18
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class WpmProPlanServiceImpl implements IWpmProPlanService {

    private final WpmProPlanMapper baseMapper;

    private final IWpmPlanTasksService tasksService;

    private final IWpmProjectsService projectsService;

    private final IWpmProPlanChangeService changeService;

    private final ISysUserService userService;

    /**
     * 查询项目计划
     *
     * @param id 主键
     * @return 项目计划
     */
    @Override
    public WpmProPlanVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询项目计划列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目计划分页列表
     */
    @Override
    public TableDataInfo<WpmProPlanVo> queryPageList(WpmProPlanBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WpmProPlan> lqw = buildQueryWrapper(bo);
        Page<WpmProPlanVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的项目计划列表
     *
     * @param bo 查询条件
     * @return 项目计划列表
     */
    @Override
    public List<WpmProPlanVo> queryList(WpmProPlanBo bo) {
        bo.setStatus(0);
        LambdaQueryWrapper<WpmProPlan> lqw = buildQueryWrapper(bo);
        List<WpmProPlanVo> result = baseMapper.selectVoList(lqw);
        result.forEach(vo -> {
            if (vo.getChargeUserId() != null) {
                vo.setChargeUserIds(vo.getChargeUserId().split(","));
                for (String userId : vo.getChargeUserIds()) {
                    vo.setChargeUser(vo.getChargeUser() == null ? userService.selectUserByEmpId(userId) : vo.getChargeUser().concat("," + userService.selectUserByEmpId(userId)));
                }
            }
            if (vo.getExternalUserId() != null) {
                vo.setExternalUserIds(vo.getExternalUserId().split(","));
            }
            vo.setChildren(baseMapper.selectVoList(new LambdaQueryWrapper<WpmProPlan>().eq(WpmProPlan::getProId, vo.getProId()).eq(WpmProPlan::getStatus, 0).eq(WpmProPlan::getParentTaskId, vo.getTaskId())));
        });
        result.forEach(vo -> {
            if (vo.getChildren() != null && !vo.getChildren().isEmpty()) {
                vo.getChildren().forEach(child -> {
                    if (child.getChargeUserId() != null) {
                        child.setChargeUserIds(child.getChargeUserId().split(","));
                    }
                    if (child.getExternalUserId() != null) {
                        child.setExternalUserIds(child.getExternalUserId().split(","));
                    }
                });
            }
        });
        return result;
    }

    private LambdaQueryWrapper<WpmProPlan> buildQueryWrapper(WpmProPlanBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WpmProPlan> lqw = Wrappers.lambdaQuery();
        lqw.eq(true, WpmProPlan::getProId, bo.getProId());
        lqw.eq(bo.getPlanStartTime() != null, WpmProPlan::getPlanStartTime, bo.getPlanStartTime());
        lqw.eq(bo.getRealStartTime() != null, WpmProPlan::getRealStartTime, bo.getRealStartTime());
        lqw.eq(bo.getPlanEndTime() != null, WpmProPlan::getPlanEndTime, bo.getPlanEndTime());
        lqw.eq(bo.getRealEndTime() != null, WpmProPlan::getRealEndTime, bo.getRealEndTime());
        lqw.eq(bo.getIsDelete() != null, WpmProPlan::getIsDelete, bo.getIsDelete());
        lqw.eq(bo.getStatus() != null, WpmProPlan::getStatus, bo.getStatus());
        lqw.eq(true, WpmProPlan::getParentTaskId, 0);
        lqw.orderByAsc(WpmProPlan::getTaskId);
        return lqw;
    }

    /**
     * 新增项目计划
     *
     * @param bo 项目计划
     * @return 是否新增成功
     */
    @Override
    @Transactional
    public Boolean insertByBo(WpmProPlanBo bo) {
        WpmProPlan add = MapstructUtils.convert(bo, WpmProPlan.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改项目计划
     *
     * @param bo 项目计划
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WpmProPlanBo bo) {
        WpmProPlan update = MapstructUtils.convert(bo, WpmProPlan.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WpmProPlan entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除项目计划信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    @Transactional
    public Boolean savePlan(WpmProPlanBo bo) {
        List<WpmProPlanBo> boList = new ArrayList<>();
        for (Long taskId : bo.getTaskIds()) {
            WpmPlanTasksVo tasksVo = tasksService.queryById(taskId);
            WpmProPlanBo planBo = new WpmProPlanBo();
            planBo.setProId(bo.getProId());
            planBo.setTaskId(taskId);
            planBo.setTaskName(tasksVo.getTaskName());
            planBo.setParentTaskId(tasksVo.getParentId());
            boList.add(planBo);
        }
        //批量新增项目计划
        //查询是否存在 如果存在、更新taskName
        boList.forEach(planBo -> {
            WpmProPlan plan = baseMapper.selectOne(new LambdaQueryWrapper<WpmProPlan>().eq(WpmProPlan::getProId, planBo.getProId()).eq(WpmProPlan::getTaskId, planBo.getTaskId()).eq(WpmProPlan::getParentTaskId, planBo.getParentTaskId()));
            if (plan != null) {
                planBo.setId(plan.getId());
                planBo.setTaskName(plan.getTaskName());
                planBo.setStatus(0);
                updateByBo(planBo);
            } else insertByBo(planBo);
        });
        //根据proId查询所有的WpmProPlan 获取taskId集合跟bo.getTaskIds()比较，如果bo.getTaskIds()小于taskId集合，则将 WpmProPlan status置为1
        List<WpmProPlan> planList = baseMapper.selectList(new LambdaQueryWrapper<WpmProPlan>().eq(WpmProPlan::getProId, bo.getProId()));
        List<Long> planTaskIds = planList.stream().map(WpmProPlan::getTaskId).toList();
        List<Long> boTaskIds = List.of(bo.getTaskIds());
        //比较planTaskIds 是否大于 boTaskIds ，如果是返回 多余的集合
        List<Long> diff = planTaskIds.stream().filter(taskId -> !boTaskIds.contains(taskId)).toList();
        if (!diff.isEmpty()) {
            baseMapper.update(null, new LambdaUpdateWrapper<WpmProPlan>().set(WpmProPlan::getStatus, 1).in(WpmProPlan::getTaskId, diff));
        }
        //baseMapper.insertBatch(MapstructUtils.convert(boList, WpmProPlan.class));
        //修改项目任务状态
        WpmProjectsVo projectsVo = projectsService.selectByProId(bo.getProId());
        if (projectsVo != null && TaskStatusEnum.INIT.getCode() == projectsVo.getTaskStatus()) {
            projectsService.updateTaskStatus(bo.getProId(), TaskStatusEnum.RUNNING.getCode());
        }
        return true;
    }

    @Override
    public Boolean changeEndTime(WpmProPlanBo bo) {
        WpmProjectsVo projectsVo = projectsService.selectByProId(bo.getProId());
        if (TaskStatusEnum.FAIL.getCode() == projectsVo.getTaskStatus()) {
            WpmProPlan plan = baseMapper.selectById(bo.getId());
            WpmProPlanChangeBo changeBo = init(plan, projectsVo);
            changeBo.setChangeBefore(DateUtil.format(plan.getPlanEndTime(), Constants.formatterDate));
            changeBo.setChangeAfter(DateUtil.format(bo.getPlanEndTime(), Constants.formatterDate));
            changeBo.setChangeColumn("计划完工时间");

            return changeService.insertByBo(changeBo);
        }
        return baseMapper.update(null, new LambdaUpdateWrapper<WpmProPlan>().eq(WpmProPlan::getId, bo.getId()).set(WpmProPlan::getPlanEndTime, bo.getPlanEndTime())) > 0;
    }

    private WpmProPlanChangeBo init(WpmProPlan plan, WpmProjectsVo projectsVo) {
        if (plan == null) {
            throw new RuntimeException("项目计划不存在");
        }

        if (projectsVo == null) {
            throw new RuntimeException("项目不存在");
        }
        if (projectsVo.getTaskStatus() == TaskStatusEnum.SUCCESS.getCode()) {
            throw new RuntimeException("项目计划已完成，不能修改计划");
        }
        WpmProPlanChangeBo changeBo = new WpmProPlanChangeBo();
        changeBo.setProId(plan.getProId());
        changeBo.setTaskId(projectsVo.getTaskId());
        changeBo.setPlanId(plan.getId());
        changeBo.setTaskName(plan.getTaskName());
        return changeBo;
    }

    @Override
    public Boolean changeStartTime(WpmProPlanBo bo) {
        WpmProjectsVo projectsVo = projectsService.selectByProId(bo.getProId());
        if (TaskStatusEnum.FAIL.getCode() == projectsVo.getTaskStatus()) {
            WpmProPlan plan = baseMapper.selectById(bo.getId());
            WpmProPlanChangeBo changeBo = init(plan, projectsVo);
            changeBo.setChangeBefore(DateUtil.format(plan.getPlanStartTime(), Constants.formatterDate));
            changeBo.setChangeAfter(DateUtil.format(bo.getPlanStartTime(), Constants.formatterDate));
            changeBo.setChangeColumn("计划开工时间");
            return changeService.insertByBo(changeBo);
        }
        return baseMapper.update(null, new LambdaUpdateWrapper<WpmProPlan>().eq(WpmProPlan::getId, bo.getId()).set(WpmProPlan::getPlanStartTime, bo.getPlanStartTime())) > 0;
    }

    @Override
    public Boolean changeChargePerson(WpmProPlanBo bo) {
        WpmProjectsVo projectsVo = projectsService.selectByProId(bo.getProId());
        if (TaskStatusEnum.FAIL.getCode() == projectsVo.getTaskStatus()) {
            WpmProPlan plan = baseMapper.selectById(bo.getId());
            WpmProPlanChangeBo changeBo = init(plan, projectsVo);
            changeBo.setChangeBefore(plan.getChargeUserId() == null ? "" : plan.getChargeUserId());
            changeBo.setChangeAfter(bo.getChargeUserIds() == null ? "" : StringUtils.join(bo.getChargeUserIds(), ','));
            changeBo.setChangeColumn("负责人");
            return changeService.insertByBo(changeBo);
        }

        return baseMapper.update(null, new LambdaUpdateWrapper<WpmProPlan>().eq(WpmProPlan::getId, bo.getId()).set(WpmProPlan::getChargeUserId, StringUtils.join(bo.getChargeUserIds(), ','))) > 0;
    }

    @Override
    public Boolean changeExternalUserId(WpmProPlanBo bo) {
        WpmProjectsVo projectsVo = projectsService.selectByProId(bo.getProId());
        if (TaskStatusEnum.FAIL.getCode() == projectsVo.getTaskStatus()) {
            WpmProPlan plan = baseMapper.selectById(bo.getId());
            WpmProPlanChangeBo changeBo = init(plan, projectsVo);
            changeBo.setChangeBefore(plan.getExternalUserId() == null ? "" : plan.getExternalUserId());
            changeBo.setChangeAfter(bo.getExternalUserIds() == null ? "" : StringUtils.join(bo.getExternalUserIds(), ','));
            changeBo.setChangeColumn("外部成员");
            return changeService.insertByBo(changeBo);
        }
        return baseMapper.update(null, new LambdaUpdateWrapper<WpmProPlan>().eq(WpmProPlan::getId, bo.getId()).set(WpmProPlan::getExternalUserId, StringUtils.join(bo.getExternalUserIds(), ','))) > 0;
    }

    @Override
    public int startPlan(WpmProPlanBo bo) {
        return baseMapper.update(null, new LambdaUpdateWrapper<WpmProPlan>().eq(WpmProPlan::getId, bo.getId()).isNull(WpmProPlan::getRealStartTime).set(WpmProPlan::getRealStartTime, DateUtil.now()).set(WpmProPlan::getStartStatus, 1));
    }

    @Override
    public int complatePlan(WpmProPlanBo bo) {
        WpmProPlan plan = baseMapper.selectById(bo.getId());
        if (plan != null) {
            if (plan.getRealStartTime() == null) {
                throw new ServiceException("任务未开始");
            }
        }
        return baseMapper.update(null, new LambdaUpdateWrapper<WpmProPlan>().eq(WpmProPlan::getId, bo.getId()).isNull(WpmProPlan::getRealEndTime).set(WpmProPlan::getRealEndTime, DateUtil.now()).set(WpmProPlan::getEndStatus, 1));
    }

    @Override
    public List<WpmProPlanVo> currentManage(WpmProPlanBo bo) {
        return baseMapper.selectVoList(new LambdaQueryWrapper<WpmProPlan>().eq(WpmProPlan::getProId, bo.getProId()).eq(WpmProPlan::getParentTaskId, 3));
    }

    @Override
    public int enable(List<Long> ids, boolean b) {
        return baseMapper.update(null, new LambdaUpdateWrapper<WpmProPlan>().in(WpmProPlan::getId, ids).and(i -> i.eq(WpmProPlan::getStatus, 0).or().ne(WpmProPlan::getStatus, 0)).setSql("status = CASE WHEN status = 0 THEN 1 ELSE 0 END"));
        //return 0;
    }

    @Override
    public void updateLambda(LambdaUpdateWrapper<WpmProPlan> set) {
        baseMapper.update(null, set);
    }

    @Override
    public TaskTreeVo initTreeSelect(WpmProPlanBo tasksBo) {
        List<WpmPlanTasksVo> tasksVos = tasksService.selectTaskList(null);
        TaskTreeVo taskTreeVo = new TaskTreeVo();
        taskTreeVo.setTasksVos(tasksService.buildTaskTreeSelect(tasksVos));
        List<Long> select = new ArrayList<>(baseMapper.selectVoList(new LambdaQueryWrapper<WpmProPlan>().eq(WpmProPlan::getStatus, 0).eq(WpmProPlan::getProId, tasksBo.getProId())).stream().map(WpmProPlanVo::getTaskId).toList());
        taskTreeVo.setCheckedKeys(select);

        return taskTreeVo;
    }

    @Override
    public List<StatisticsPlanVo> queryStatisticsPlan(String proId) {
        List<StatisticsPlanVo> statisticsPlanVos = baseMapper.getPlanByProId(proId);
        statisticsPlanVos.forEach(statisticsPlanVo -> {
            if (StringUtils.isNotBlank(statisticsPlanVo.getTaskCharge())) {
                for (String userId : statisticsPlanVo.getTaskCharge().split(",")) {
                    statisticsPlanVo.setTaskChargeName(statisticsPlanVo.getTaskChargeName() == null ? userService.selectUserByEmpId(userId) : statisticsPlanVo.getTaskChargeName().concat("," + userService.selectUserByEmpId(userId)));
                }
            }
        });
        return statisticsPlanVos;
    }

    @Override
    public List<WpmProPlanVo> getByPlanEndTime(int i) {
        return baseMapper.selectVoList(new LambdaQueryWrapper<WpmProPlan>().eq(WpmProPlan::getPlanEndTime, DateUtil.offsetDay(DateUtil.date(), i)).isNull(WpmProPlan::getRealEndTime));
    }

    @Override
    public List<WpmProPlanVo> monthReport(MonthReportBo bo) {
        List<WpmProPlanVo> planVos = baseMapper.selectVoList(new LambdaQueryWrapper<WpmProPlan>().eq(WpmProPlan::getProId, bo.getProId()).
            between(WpmProPlan::getPlanEndTime, bo.getDateStart(), bo.getDateEnd()));
        planVos.forEach(vo -> {
            if (vo.getChargeUserId() != null) {
                vo.setChargeUserIds(vo.getChargeUserId().split(","));
                for (String userId : vo.getChargeUserIds()) {
                    vo.setChargeUser(vo.getChargeUser() == null ? userService.selectUserByEmpId(userId) : vo.getChargeUser().concat("," + userService.selectUserByEmpId(userId)));
                }
            }
        } );
        return planVos;
    }

    @Override
    public List<WpmProPlanVo> waitingTask(MonthReportBo bo) {
        List<WpmProPlanVo> wpmProPlanVos = baseMapper.selectVoList(new LambdaQueryWrapper<WpmProPlan>().eq(WpmProPlan::getProId, bo.getProId()).le(WpmProPlan::getPlanEndTime, bo.getDateEnd()).isNull(WpmProPlan::getRealEndTime));
        wpmProPlanVos.forEach(vo -> {
            if (vo.getChargeUserId() != null) {
                vo.setChargeUserIds(vo.getChargeUserId().split(","));
                for (String userId : vo.getChargeUserIds()) {
                    vo.setChargeUser(vo.getChargeUser() == null ? userService.selectUserByEmpId(userId) : vo.getChargeUser().concat("," + userService.selectUserByEmpId(userId)));
                }
            }
        });

        return wpmProPlanVos;
    }
}
