package com.cyth.business.plan_change.domain;

import com.cyth.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 项目计划变更对象 wpm_pro_plan_change
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wpm_pro_plan_change")
public class WpmProPlanChange extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 项目id
     */
    private String proId;

    /**
     * 计划id
     */
    private Long planId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 变更字段
     */
    private String changeColumn;

    /**
     * 变更前
     */
    private String changeBefore;

    /**
     * 变更后
     */
    private String changeAfter;


}
