package com.cyth.business.file_index.service.impl;

import com.cyth.business.file_index.domain.WpmProDocumentIndex;
import com.cyth.business.file_index.domain.bo.WpmProDocumentIndexBo;
import com.cyth.business.file_index.domain.vo.WpmProDocumentIndexVo;
import com.cyth.business.file_index.mapper.WpmProDocumentIndexMapper;
import com.cyth.business.file_index.service.IWpmProDocumentIndexService;
import com.cyth.common.core.utils.MapstructUtils;
import com.cyth.common.core.utils.StringUtils;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 项目资料目录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-16
 */
@RequiredArgsConstructor
@Service
public class WpmProDocumentIndexServiceImpl implements IWpmProDocumentIndexService {

    private final WpmProDocumentIndexMapper baseMapper;

    /**
     * 查询项目资料目录
     *
     * @param id 主键
     * @return 项目资料目录
     */
    @Override
    public WpmProDocumentIndexVo queryById(Long id){
        WpmProDocumentIndexVo vo =baseMapper.selectVoById(id);
        if (StringUtils.isNotBlank(vo.getFileType())){
            vo.setFileTypes(vo.getFileType().split(","));
        }
        return vo;
    }

    /**
     * 分页查询项目资料目录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目资料目录分页列表
     */
    @Override
    public TableDataInfo<WpmProDocumentIndexVo> queryPageList(WpmProDocumentIndexBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WpmProDocumentIndex> lqw = buildQueryWrapper(bo);
        Page<WpmProDocumentIndexVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        result.getRecords().forEach(item ->{
            if (StringUtils.isNotBlank(item.getFileType())){
                item.setFileTypes(item.getFileType().split(","));
            }
        });
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的项目资料目录列表
     *
     * @param bo 查询条件
     * @return 项目资料目录列表
     */
    @Override
    public List<WpmProDocumentIndexVo> queryList(WpmProDocumentIndexBo bo) {
        LambdaQueryWrapper<WpmProDocumentIndex> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WpmProDocumentIndex> buildQueryWrapper(WpmProDocumentIndexBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WpmProDocumentIndex> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), WpmProDocumentIndex::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getCode()), WpmProDocumentIndex::getCode, bo.getCode());
        lqw.eq(bo.getOrderNum() != null, WpmProDocumentIndex::getOrderNum, bo.getOrderNum());
        lqw.eq(bo.getParentId() != null, WpmProDocumentIndex::getParentId, bo.getParentId());
        lqw.eq(bo.getIsDelete() != null, WpmProDocumentIndex::getIsDelete, bo.getIsDelete());
        lqw.orderByAsc(WpmProDocumentIndex::getId);
        return lqw;
    }

    /**
     * 新增项目资料目录
     *
     * @param bo 项目资料目录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WpmProDocumentIndexBo bo) {
        if (bo.getFileTypes()!=null){
            bo.setFileType(String.join(",",bo.getFileTypes()));
        }
        WpmProDocumentIndex add = MapstructUtils.convert(bo, WpmProDocumentIndex.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改项目资料目录
     *
     * @param bo 项目资料目录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WpmProDocumentIndexBo bo) {
        if (bo.getFileTypes()!=null){
            bo.setFileType(String.join(",",bo.getFileTypes()));
        }
        WpmProDocumentIndex update = MapstructUtils.convert(bo, WpmProDocumentIndex.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WpmProDocumentIndex entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除项目资料目录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
