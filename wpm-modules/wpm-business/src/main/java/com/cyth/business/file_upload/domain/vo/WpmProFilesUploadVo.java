package com.cyth.business.file_upload.domain.vo;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cyth.business.file_upload.domain.WpmProFilesUpload;
import com.cyth.common.excel.annotation.ExcelDictFormat;
import com.cyth.common.excel.convert.ExcelDictConvert;
import com.cyth.common.mybatis.core.domain.BaseEntity;
import com.cyth.common.translation.annotation.Translation;
import com.cyth.common.translation.constant.TransConstant;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 项目资料上传视图对象 wpm_pro_files_upload
 *
 * <AUTHOR>
 * @date 2024-10-16
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WpmProFilesUpload.class)
public class WpmProFilesUploadVo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private String proId;

    /**
     * 文件名称
     */
    @ExcelProperty(value = "文件名称")
    private String indexName;

    private String fileName;

    /**
     * oss文件id
     */
    @ExcelProperty(value = "oss文件id")
    private String ossIds;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 任务id
     */
    @ExcelProperty(value = "任务id")
    private String taskId;

    private Long proFileId;
    @Translation(type = TransConstant.USER_ID_TO_NICKNAME, mapper = "createBy")
    private String createByName;
}
