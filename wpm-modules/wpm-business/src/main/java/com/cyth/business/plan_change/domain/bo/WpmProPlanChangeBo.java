package com.cyth.business.plan_change.domain.bo;


import com.cyth.business.plan_change.domain.WpmProPlanChange;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 项目计划变更业务对象 wpm_pro_plan_change
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WpmProPlanChange.class, reverseConvertGenerate = false)
public class WpmProPlanChangeBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 项目id
     */
    @NotBlank(message = "项目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String proId;

    /**
     * 计划id
     */
    @NotBlank(message = "计划id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long planId;

    /**
     * 任务id
     */
    @NotBlank(message = "任务id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String  taskId;

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String taskName;

    /**
     * 变更字段
     */
    @NotBlank(message = "变更字段不能为空", groups = { AddGroup.class, EditGroup.class })
    private String changeColumn;

    /**
     * 变更前
     */
    @NotBlank(message = "变更前不能为空", groups = { AddGroup.class, EditGroup.class })
    private String changeBefore;

    /**
     * 变更后
     */
    @NotBlank(message = "变更后不能为空", groups = { AddGroup.class, EditGroup.class })
    private String changeAfter;


}
