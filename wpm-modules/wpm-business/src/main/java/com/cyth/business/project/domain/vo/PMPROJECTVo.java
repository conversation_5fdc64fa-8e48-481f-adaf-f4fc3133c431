package com.cyth.business.project.domain.vo;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.cyth.business.project.domain.PMPROJECT;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cyth.common.excel.annotation.ExcelDictFormat;
import com.cyth.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 项目视图对象 PMPROJECT
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PMPROJECT.class)
public class PMPROJECTVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * PMPROJECT_PRJID
     */
    private String pmprojectPrjid;

    /**
     * PMPROJECT_CREATEDT
     */
    private Date pmprojectCreatedt;

    /**
     * PMPROJECT_CREATEBY
     */
    private String pmprojectCreateby;

    /**
     * PMPROJECT_CODE 项目编码
     */
    private String pmprojectCode;

    /**
     * PMPROJECT_NAME
     */
    private String pmprojectName;

    //PMPROJECT_ENTITY
    private String pmprojectEntity;
    //ASORG_ORGNAME
    private String asorgOrgname;

    /**
     * PMPROJECT_STATUS
     */
    private Long pmprojectStatus;

    //PMPROJECT_MANAGER  项目经理
    private String pmprojectManager;

    //PMPROJECT_PLANSDATE 计划开工日期
    private Date pmprojectPlansdate;

    //PMPRJREQ_CUSTID
    private String pmprjreqCustid;
    //CMCUST_NAME
    private String cmcustName;


    //PMPROJECT_MUSTFDATE  完工日期
    private Date pmprojectMustfdate;

    //PMPRJREQ_ESTDATE 立项日期
    private Date pmprjreqEstdate;

    //OAXMKZ11_XMFZGCS 项目负责工程师
    private String oaxmkzgcs;

    private String hzlx;

    private String xmgm;

    private String xmxs;

    private String glyq;
    private String jsdw;

    private String zyzb;

    //PMPRJREQ_NOTE
    private String pmprjreqNote;


}
