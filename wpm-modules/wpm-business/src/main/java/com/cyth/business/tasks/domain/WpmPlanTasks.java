package com.cyth.business.tasks.domain;

import com.cyth.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 任务对象 wpm_pla_tasks
 *
 * <AUTHOR>
 * @date 2024-09-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wpm_plan_tasks")
public class WpmPlanTasks extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 任务名称
     */
    private String taskName;


    /**
     * 父任务id
     */
    private Long parentId;

    /**
     * 任务状态
     */
    private Long taskStatus;

    /**
     * 备注
     */
    private String remark;

    //order_num
    private Integer orderNum;


}
