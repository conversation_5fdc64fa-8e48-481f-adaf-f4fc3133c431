package com.cyth.business.project.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.cyth.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 项目对象 wpm_projects
 *
 * <AUTHOR>
 * @date 2024-09-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wpm_projects")
public class WpmProjects extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 项目id，来之诺明
     */
    private String proId;

    /**
     * 项目编码
     */
    private String proCode;

    /**
     * 名称
     */
    private String proName;

    /**
     * 部门id
     */
    private String proDeptId;

    /**
     * 部门名称
     */
    private String proDeptName;

    /**
     * 状态  1 提前开工 2 未开工 3 延迟开工 4 正常开工
     */
    private Long proStatus;
    /**
     * 完工状态 1 提前完工 2 未完工 3 延迟完工 4正常完工
     */
    private Long complateStatus;

    //task_status
    private Long taskStatus;

    private String taskId;

    //audit_status
    private String auditStatus;



    /**
     * 项目负责人
     */
    private String proManagerId;

    /**
     * 计划开始时间
     */
    private Date proPlanStartTime;

    /**
     * 实际开始时间
     */
    private Date proRealStartTime;

    /**
     * 必须完成时间
     */
    private Date proFinishTime;

    /**
     * 实际完成时间
     */
    private Date proRealFinishTime;

    /**
     * 客户id
     */
    private String proCustomId;

    /**
     * 客户名称
     */
    private String proCustomName;

    /**
     * 立项时间
     */
    private Date proEsTime;

    /**
     * 负责工程师
     */
    private String fzgcs;

    /**
     * 合作类型
     */
    private String proCoType;

    /**
     * 项目规模
     */
    private String proScale;

    /**
     * 项目形式
     */
    private String proForm;

    /**
     * 管理要求
     */
    private String manageNeeds;

    /**
     * 建设单位
     */
    private String constructCompany;

    /**
     * 主要指标
     */
    private String mainIndicator;

    /**
     * 备注
     */
    private String remark;

    //total_investment总投资金额
    private String totalInvestment;

    //control_price控制价
    private String controlPrice;
    //contract_price合同价
    private String contractPrice;
}
