package com.cyth.business.emp.mapper;


import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.cyth.business.emp.domain.HREMP;
import com.cyth.business.emp.domain.vo.HREMPVo;
import com.cyth.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 员工Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
public interface HREMPMapper extends BaseMapperPlus<HREMP, HREMPVo> {

    @InterceptorIgnore(tenantLine = "true", dataPermission = "false")
    List<HREMPVo> getList(String projectId);
    @InterceptorIgnore(tenantLine = "true", dataPermission = "false")
    List<HREMPVo> getManagerList(@Param("managerId") String pmprojectManager, @Param("gcs") String gcs);
}
