package com.cyth.business.patrol.service;


import com.cyth.business.patrol.domain.bo.WpmProPatrolPicBo;
import com.cyth.business.patrol.domain.vo.WpmProPatrolPicVo;
import com.cyth.business.patrol.domain.vo.WpmProPatrolStatisticsVo;
import com.cyth.business.plan_change.domain.bo.MonthReportBo;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 巡检图片Service接口
 *
 * <AUTHOR>
 * @date 2024-10-10
 */
public interface IWpmProPatrolPicService {

    /**
     * 查询巡检图片
     *
     * @param id 主键
     * @return 巡检图片
     */
    WpmProPatrolPicVo queryById(Long id);

    /**
     * 分页查询巡检图片列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 巡检图片分页列表
     */
    TableDataInfo<WpmProPatrolPicVo> queryPageList(WpmProPatrolPicBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的巡检图片列表
     *
     * @param bo 查询条件
     * @return 巡检图片列表
     */
    List<WpmProPatrolPicVo> queryList(WpmProPatrolPicBo bo);

    /**
     * 新增巡检图片
     *
     * @param bo 巡检图片
     * @return 是否新增成功
     */
    Boolean insertByBo(WpmProPatrolPicBo bo);

    /**
     * 修改巡检图片
     *
     * @param bo 巡检图片
     * @return 是否修改成功
     */
    Boolean updateByBo(WpmProPatrolPicBo bo);

    /**
     * 校验并批量删除巡检图片信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    List<WpmProPatrolPicVo> getMonthReport(MonthReportBo bo);

    List<WpmProPatrolStatisticsVo> getPatrolStatistics(MonthReportBo bo);
}
