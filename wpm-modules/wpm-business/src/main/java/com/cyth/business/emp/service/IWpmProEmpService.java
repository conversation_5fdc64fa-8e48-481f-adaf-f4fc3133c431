package com.cyth.business.emp.service;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cyth.business.emp.domain.WpmProEmp;
import com.cyth.business.emp.domain.bo.WpmProEmpBo;
import com.cyth.business.emp.domain.vo.WpmProEmpVo;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.cyth.system.domain.bo.SysUserBo;
import com.cyth.system.domain.vo.SysUserVo;
import software.amazon.awssdk.services.s3.endpoints.internal.Value;

import java.util.Collection;
import java.util.List;

/**
 * 项目雇员Service接口
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
public interface IWpmProEmpService {

    /**
     * 查询项目雇员
     *
     * @param id 主键
     * @return 项目雇员
     */
    WpmProEmpVo queryById(Long id);

    /**
     * 分页查询项目雇员列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目雇员分页列表
     */
    TableDataInfo<WpmProEmpVo> queryPageList(WpmProEmpBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的项目雇员列表
     *
     * @param bo 查询条件
     * @return 项目雇员列表
     */
    List<WpmProEmpVo> queryList(WpmProEmpBo bo);

    /**
     * 新增项目雇员
     *
     * @param bo 项目雇员
     * @return 是否新增成功
     */
    Boolean insertByBo(WpmProEmpBo bo);

    /**
     * 修改项目雇员
     *
     * @param bo 项目雇员
     * @return 是否修改成功
     */
    Boolean updateByBo(WpmProEmpBo bo);

    /**
     * 校验并批量删除项目雇员信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Boolean syncFromNoma(String proId);

    Boolean insertOrUpdateBatch(List<WpmProEmpBo> vos,List<SysUserBo> sysUserVos);

    List<WpmProEmpVo> getUserByProId(String proId);

    int countByProId(String proId);

    List<WpmProEmpVo> selectEmpDeptIdByEmpId(String empId);

    WpmProEmpVo selectVoOne(LambdaQueryWrapper<WpmProEmp> eq);

    void insert(WpmProEmp wpmProEmp);

    void update(WpmProEmp wpmProEmp, LambdaUpdateWrapper<WpmProEmp> eq);

    int updateProEmpStatus(WpmProEmpBo bo);
}
