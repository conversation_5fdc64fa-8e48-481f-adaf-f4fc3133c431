package com.cyth.business.plan.domain.vo;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.IdUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.uuid.impl.UUIDUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 版本号
 * @date 2024年10月25日 12:20
 * @description
 */
@Data
public class StatisticsPlanVo  implements Serializable {


    //private String id= IdUtil.simpleUUID();
    /**
     * 主任务id
     */
    private Long taskId;
    /**
     * 主任务名称
     */
    private String taskName;
    /**

    /**
     * 任务负责人
     */
    private String taskCharge;

    private String taskChargeName;

    /**
     * 时间状态
     */
    private String timeStyle;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;
    /**
     * 状态
     */
    private int status;

    /**
     * 天数
     */
    private Integer dayLen;

}
