package com.cyth.business.files.domain;

import com.cyth.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 项目资料对象 wpm_pro_files
 *
 * <AUTHOR>
 * @date 2024-09-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wpm_pro_files")
public class WpmProFiles extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * oss文件id
     */
    private String ossId;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件编码
     */
    private String fileCode;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 是否获取
     */
    private Integer isGet;

    /**
     * 状态
     */
    private String status;

    /**
     * 项目id
     */
    private String proId;

    /**
     * 项目名称
     */
    private String proName;

    /**
     * 备注
     */
    private String remark;

    //parent_id
    private Long parentId;

    private String taskId;

    /**
     * 是否文件
     */
    private Long isFile;
}
