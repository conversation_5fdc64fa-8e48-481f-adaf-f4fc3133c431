package com.cyth.business.emp.domain.vo;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cyth.business.emp.domain.WpmProEmp;
import com.cyth.common.excel.annotation.ExcelDictFormat;
import com.cyth.common.excel.convert.ExcelDictConvert;
import com.cyth.common.mybatis.core.domain.BaseEntity;
import com.cyth.common.translation.annotation.Translation;
import com.cyth.common.translation.constant.TransConstant;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 项目雇员视图对象 wpm_pro_emp
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WpmProEmp.class)
public class WpmProEmpVo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private String proId;

    /**
     * 雇员id
     */
    @ExcelProperty(value = "雇员id")
    private String empId;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    private String empName;

    /**
     * 类型
     */
    @ExcelProperty(value = "类型")
    private String empType;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private String empStatus;

    /**
     * 电话
     */
    @ExcelProperty(value = "电话")
    private String empPhone;

    @Translation(type = TransConstant.USER_ID_TO_NICKNAME, mapper = "createBy")
    private String creator;

    private String remark;

}
