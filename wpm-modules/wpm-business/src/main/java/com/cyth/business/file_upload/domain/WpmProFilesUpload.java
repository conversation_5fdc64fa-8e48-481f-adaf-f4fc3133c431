package com.cyth.business.file_upload.domain;

import com.cyth.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 项目资料上传对象 wpm_pro_files_upload
 *
 * <AUTHOR>
 * @date 2024-10-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wpm_pro_files_upload")
public class WpmProFilesUpload extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 项目id
     */
    private String proId;

    /**
     * 目录名称
     */
    private String indexName;
    /**
     * 文件名称
     */
    private String fileName;

    /**
     * oss文件id
     */
    private String ossIds;

    /**
     * 状态
     */
    private String status;

    /**
     * 任务id
     */
    private String taskId;

    //pro_file_id
    private Long proFileId;


}
