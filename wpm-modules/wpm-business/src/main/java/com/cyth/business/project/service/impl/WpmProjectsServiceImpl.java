package com.cyth.business.project.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.cyth.business.emp.domain.WpmProEmp;
import com.cyth.business.emp.domain.bo.WpmProEmpBo;
import com.cyth.business.emp.domain.vo.HREMPVo;
import com.cyth.business.emp.domain.vo.WpmProEmpVo;
import com.cyth.business.emp.service.IHREMPService;
import com.cyth.business.emp.service.IWpmProEmpService;
import com.cyth.business.enums.pro.EmpTypeEnums;
import com.cyth.business.enums.pro.TaskStatusEnum;
import com.cyth.business.patrol.domain.WpmProPatrolPic;
import com.cyth.business.plan.domain.WpmProPlan;
import com.cyth.business.plan.service.IWpmProPlanService;
import com.cyth.business.plan_change.domain.WpmProPlanChange;
import com.cyth.business.plan_change.domain.vo.WpmProPlanChangeVo;
import com.cyth.business.plan_change.service.IWpmProPlanChangeService;
import com.cyth.business.plan_history.domain.WpmProPlanHistory;
import com.cyth.business.plan_history.domain.vo.WpmProPlanHistoryVo;
import com.cyth.business.plan_history.service.IWpmProPlanHistoryService;
import com.cyth.business.project.domain.PMPROJECT;
import com.cyth.business.project.domain.WpmProjects;
import com.cyth.business.project.domain.bo.WpmProjectsBo;
import com.cyth.business.project.domain.vo.PMPROJECTVo;
import com.cyth.business.project.domain.vo.WpmProjectsVo;
import com.cyth.business.project.mapper.WpmProjectsMapper;
import com.cyth.business.project.service.IPMPROJECTService;
import com.cyth.business.project.service.IWpmProjectsService;
import com.cyth.common.core.domain.event.ProcessEvent;
import com.cyth.common.core.domain.event.ProcessTaskEvent;
import com.cyth.common.core.enums.BusinessStatusEnum;
import com.cyth.common.core.exception.ServiceException;
import com.cyth.common.core.utils.MapstructUtils;
import com.cyth.common.core.utils.StringUtils;
import com.cyth.common.core.utils.ValidatorUtils;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cyth.common.satoken.utils.LoginHelper;
import com.cyth.system.domain.SysUser;
import com.cyth.system.domain.bo.SysUserBo;
import com.cyth.system.domain.vo.SysUserVo;
import com.cyth.system.service.ISysUserService;
import com.cyth.workflow.common.constant.ProcessInstanceKeysConstant;
import com.cyth.workflow.domain.bo.CompleteTaskBo;
import com.cyth.workflow.domain.bo.StartProcessBo;
import com.cyth.workflow.service.IActTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-13
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class WpmProjectsServiceImpl implements IWpmProjectsService {

    private final WpmProjectsMapper baseMapper;

    private final IPMPROJECTService pmprojectService;

    private final IHREMPService ihrempService;

    private final ISysUserService sysUserService;

    private final IWpmProEmpService empService;

    private final IActTaskService actTaskService;

    private final IWpmProPlanHistoryService historyService;

    private final IWpmProPlanChangeService planChangeService;

    private final IdentifierGenerator idGenerator;




    /**
     * 查询项目
     *
     * @param id 主键
     * @return 项目
     */
    @Override
    public WpmProjectsVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询项目列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目分页列表
     */
    @Override
    public TableDataInfo<WpmProjectsVo> queryPageList(WpmProjectsBo bo, PageQuery pageQuery) {
        //bo.setProDeptId("17");
        LambdaQueryWrapper<WpmProjects> lqw = buildQueryWrapper(bo);
        Page<WpmProjectsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        result.getRecords().forEach(vo -> {
            vo.setEmpNum(empService.countByProId(vo.getProId()));
        });
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的项目列表
     *
     * @param bo 查询条件
     * @return 项目列表
     */
    @Override
    public List<WpmProjectsVo> queryList(WpmProjectsBo bo) {
        //bo.setProDeptId("17");
        LambdaQueryWrapper<WpmProjects> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WpmProjects> buildQueryWrapper(WpmProjectsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WpmProjects> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProId() != null, WpmProjects::getProId, bo.getProId());
        lqw.eq(StringUtils.isNotBlank(bo.getProCode()), WpmProjects::getProCode, bo.getProCode());
        lqw.like(StringUtils.isNotBlank(bo.getProName()), WpmProjects::getProName, bo.getProName());
        //lqw.eq(bo.getProDeptId() != null, WpmProjects::getProDeptId, bo.getProDeptId());
        lqw.like(StringUtils.isNotBlank(bo.getProDeptName()), WpmProjects::getProDeptName, bo.getProDeptName());
        lqw.eq(bo.getProStatus() != null, WpmProjects::getProStatus, bo.getProStatus());
        lqw.eq(bo.getProManagerId() != null, WpmProjects::getProManagerId, bo.getProManagerId());
        lqw.eq(bo.getProPlanStartTime() != null, WpmProjects::getProPlanStartTime, bo.getProPlanStartTime());
        lqw.eq(bo.getProRealStartTime() != null, WpmProjects::getProRealStartTime, bo.getProRealStartTime());
        lqw.eq(bo.getProFinishTime() != null, WpmProjects::getProFinishTime, bo.getProFinishTime());
        lqw.eq(bo.getProRealFinishTime() != null, WpmProjects::getProRealFinishTime, bo.getProRealFinishTime());
        lqw.eq(bo.getProCustomId() != null, WpmProjects::getProCustomId, bo.getProCustomId());
        lqw.like(StringUtils.isNotBlank(bo.getProCustomName()), WpmProjects::getProCustomName, bo.getProCustomName());
        lqw.eq(bo.getProEsTime() != null, WpmProjects::getProEsTime, bo.getProEsTime());
        lqw.eq(bo.getFzgcs() != null, WpmProjects::getFzgcs, bo.getFzgcs());
        lqw.eq(StringUtils.isNotBlank(bo.getProCoType()), WpmProjects::getProCoType, bo.getProCoType());
        lqw.eq(StringUtils.isNotBlank(bo.getProScale()), WpmProjects::getProScale, bo.getProScale());
        lqw.eq(StringUtils.isNotBlank(bo.getProForm()), WpmProjects::getProForm, bo.getProForm());
        lqw.eq(StringUtils.isNotBlank(bo.getManageNeeds()), WpmProjects::getManageNeeds, bo.getManageNeeds());
        lqw.eq(StringUtils.isNotBlank(bo.getConstructCompany()), WpmProjects::getConstructCompany, bo.getConstructCompany());
        lqw.eq(StringUtils.isNotBlank(bo.getMainIndicator()), WpmProjects::getMainIndicator, bo.getMainIndicator());
        lqw.in(true, WpmProjects::getProDeptId, Arrays.asList("5", "17"));
        if (!LoginHelper.isSuperAdmin()&& !Objects.requireNonNull(LoginHelper.getLoginUser()).getRolePermission().contains("globalManager")) {
            List<String> proIds = new ArrayList<>();
            if (Objects.requireNonNull(LoginHelper.getLoginUser()).getEmpId() != null) {
                List<WpmProEmpVo> empVos = empService.selectEmpDeptIdByEmpId(LoginHelper.getLoginUser().getEmpId());
                proIds.addAll(empVos.stream().map(WpmProEmpVo::getProId).toList());
            }
            lqw.in(true, WpmProjects::getProId, proIds);
        }
        return lqw;
    }

    /**
     * 新增项目
     *
     * @param bo 项目
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WpmProjectsBo bo) {
        WpmProjects add = MapstructUtils.convert(bo, WpmProjects.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改项目
     *
     * @param bo 项目
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WpmProjectsBo bo) {
        WpmProjects update = MapstructUtils.convert(bo, WpmProjects.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WpmProjects entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除项目信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public TableDataInfo<WpmProjectsVo> syncFromNoma(WpmProjectsBo bp, PageQuery pageQuery) {
        TableDataInfo<PMPROJECTVo> pmRes = pmprojectService.queryList(pageQuery, null);
        List<WpmProjectsVo> wpmProjectsBos = new ArrayList<>();
        List<WpmProEmpBo> vos = new ArrayList<>();
        List<SysUserBo> sysUserBos = new ArrayList<>();
        if (pmRes != null && pmRes.getRows() != null) {
            for (PMPROJECTVo pro : pmRes.getRows()) {
                WpmProjectsVo bo = new WpmProjectsVo();
                bo.setProId(pro.getPmprojectPrjid());
                bo.setProCode(pro.getPmprojectCode());
                bo.setProName(pro.getPmprojectName());
                bo.setProDeptId(pro.getPmprojectEntity());
                bo.setProDeptName(pro.getAsorgOrgname());
                bo.setProStatus(pro.getPmprojectStatus());
                bo.setProManagerId(pro.getPmprojectManager());
                bo.setProPlanStartTime(pro.getPmprojectPlansdate());
                bo.setProFinishTime(pro.getPmprojectMustfdate());
                bo.setProEsTime(pro.getPmprjreqEstdate());
                bo.setProCoType(pro.getHzlx());
                bo.setProScale(pro.getXmgm());
                bo.setProForm(pro.getXmxs());
                bo.setManageNeeds(pro.getGlyq());
                bo.setConstructCompany(pro.getJsdw());
                bo.setMainIndicator(pro.getZyzb());
                bo.setFzgcs(pro.getOaxmkzgcs());
                bo.setProCustomId(pro.getPmprjreqCustid());
                bo.setProCustomName(pro.getCmcustName());
                bo.setRemark(pro.getPmprjreqNote());
                ValidatorUtils.validate(bo, AddGroup.class);
                wpmProjectsBos.add(bo);
                empService.syncFromNoma(bo.getProId());
                List<HREMPVo> hrEmpVos = ihrempService.querByEmpId(pro.getPmprojectManager(), pro.getOaxmkzgcs());
                if (hrEmpVos != null && hrEmpVos.size() > 0) {
                    for (HREMPVo hrempVo : hrEmpVos) {
                        WpmProEmpBo empBo = new WpmProEmpBo();
                        empBo.setProId(bo.getProId());
                        if (hrempVo.getHrempEmpid().equals(bo.getProManagerId())){
                            empBo.setRemark(empBo.getRemark()==null?"项目负责人":"项目负责人，"+empBo.getRemark());
                        }
                        if (hrempVo.getHrempEmpid().equals(bo.getFzgcs())){
                            empBo.setRemark(empBo.getRemark()==null?"负责工程师":"负责工程师，"+empBo.getRemark());
                        }
                        empBo.setEmpId(hrempVo.getHrempEmpid());
                        empBo.setEmpName(hrempVo.getHrempName());
                        empBo.setEmpType(EmpTypeEnums.INNER.getCode());
                        empBo.setEmpStatus(hrempVo.getHrempStatus());
                        empBo.setEmpPhone(hrempVo.getHrempPhonec());
                        ValidatorUtils.validate(empBo, AddGroup.class);
                        vos.add(empBo);
                        SysUserBo sysUserBo = new SysUserBo();
                        sysUserBo.setDeptId(Long.parseLong(hrempVo.getHrempOrg()));
                        sysUserBo.setUserName(hrempVo.getHrempEmpcode());
                        sysUserBo.setNickName(hrempVo.getHrempName());
                        sysUserBo.setPhonenumber(hrempVo.getHrempPhonec());
                        sysUserBo.setEmail(hrempVo.getEmail());
                        sysUserBo.setStatus("0");//全部激活
                        sysUserBo.setEmpId(hrempVo.getHrempEmpid());
                        ValidatorUtils.validate(sysUserBo, AddGroup.class);
                        sysUserBos.add(sysUserBo);
                    }

                }
            }
            insertFromNoma(wpmProjectsBos, sysUserBos, vos);
        }
        TableDataInfo<WpmProjectsVo> tableDataInfo = TableDataInfo.build(wpmProjectsBos);
        tableDataInfo.setTotal(pmRes.getTotal());
        return null;
    }

    @Override
    @Transactional
    public Boolean insertFromNoma(List<WpmProjectsVo> list, List<SysUserBo> sysUserBos, List<WpmProEmpBo> empBos) {
        for (WpmProjectsVo bo : list) {
            WpmProjectsVo vo = baseMapper.selectVoOne(new LambdaQueryWrapper<WpmProjects>().eq(WpmProjects::getProId, bo.getProId()));
            WpmProjects wpmProjects = MapstructUtils.convert(bo, WpmProjects.class);
            wpmProjects.setCreateDept(Long.parseLong(bo.getProDeptId()));
            if (vo == null) {
                baseMapper.insert(wpmProjects);
            } else {
                baseMapper.update(wpmProjects, new LambdaQueryWrapper<WpmProjects>().eq(WpmProjects::getProId, bo.getProId()));
            }
        }
        for (WpmProEmpBo bo : empBos) {
            WpmProEmp wpmProEmp = MapstructUtils.convert(bo, WpmProEmp.class);
            WpmProEmpVo wpmProEmpVo = empService.selectVoOne(new LambdaQueryWrapper<WpmProEmp>()
                .eq(WpmProEmp::getProId, bo.getProId()).eq(WpmProEmp::getEmpId, bo.getEmpId()));
            if (wpmProEmpVo == null) {
                empService.insert(wpmProEmp);
            } else {
                empService.update(wpmProEmp, new LambdaUpdateWrapper<WpmProEmp>().eq(WpmProEmp::getProId, bo.getProId()).eq(WpmProEmp::getEmpId, bo.getEmpId()));
            }
        }
        for (SysUserBo userbo : sysUserBos) {
            SysUser user = MapstructUtils.convert(userbo, SysUser.class);
            user.setPassword(BCrypt.hashpw("123456"));
            SysUserVo sysUserVo = sysUserService.selectVoOne(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getUserName, userbo.getUserName()).eq(SysUser::getEmpId, userbo.getEmpId()));
            if (sysUserVo == null) {
                user.setUserId(idGenerator.nextId(null).longValue());
                sysUserService.insert(user);
                //增加角色 INSERT INTO sys_user_role (user_id, role_id) VALUES (1834897486022500353, 1915607503577673729) 角色不定內部角色
                sysUserService.insertUserRole(user.getUserId(), 1915607503577673729L);
            } else
                user.setPassword(null);
                sysUserService.update(user, new LambdaUpdateWrapper<SysUser>().eq(SysUser::getUserName, userbo.getUserName()).eq(SysUser::getEmpId, userbo.getEmpId()));
        }
        return true;
    }

    @Override
    public void updateTaskStatus(String proId, int code) {
        baseMapper.update(null, new LambdaUpdateWrapper<WpmProjects>().eq(WpmProjects::getProId, proId).set(WpmProjects::getTaskStatus, code));
    }

    @Override
    @Transactional
    public int updateProjectStatus(WpmProjectsBo bo) {
        WpmProjectsVo projectsVo = baseMapper.selectVoById(bo.getId());
        /*if (projectsVo.getTaskStatus() != TaskStatusEnum.SUCCESS.getCode()) {
            if (bo.getTaskStatus() != TaskStatusEnum.SUBMIT_CHANGE.getCode()) {
                throw new ServiceException("不能重复变更");
            }
        }*/
        if(bo.getTaskStatus()==TaskStatusEnum.SUBMIT_CHANGE.getCode()||bo.getTaskStatus()==TaskStatusEnum.SUCCESS.getCode()){
            //检查必填项目

        }
        LambdaUpdateWrapper<WpmProjects> set = new LambdaUpdateWrapper<WpmProjects>().eq(WpmProjects::getId, bo.getId()).set(WpmProjects::getTaskStatus, bo.getTaskStatus());
        if (bo.getTaskStatus() == TaskStatusEnum.FAIL.getCode()) {
            WpmProPlanHistoryVo historyVo = historyService.addChange(projectsVo);
            //启动工作流
            StartProcessBo processBo = new StartProcessBo();
            processBo.setBusinessKey(historyVo.getId().toString());
            processBo.setTableName(ProcessInstanceKeysConstant.PRO_PLAN_TASK_CHANGE);
            Map<String, Object> variables = new HashMap<>();
            processBo.setVariables(variables);
            Map<String, Object> res = actTaskService.startWorkFlow(processBo);
            if (!res.isEmpty()) {
                set.set(res.get("taskId") != null, WpmProjects::getTaskId, res.get("taskId").toString());
                historyService.updateTaskId(historyVo.getId(), res.get("taskId").toString());
            }

        }
        if (bo.getTaskStatus() == TaskStatusEnum.SUBMIT_CHANGE.getCode()) {
            //WpmProPlanHistoryVo changeVo = historyService.getByProject(projectsVo);
            CompleteTaskBo completeTaskBo = new CompleteTaskBo();
            completeTaskBo.setTaskId(projectsVo.getTaskId());
            completeTaskBo.setMessageType(Collections.singletonList("1"));
            actTaskService.completeTask(completeTaskBo);
        }
        return baseMapper.update(null, set);
    }

    @Override
    public int proStart(Long id) {
        WpmProjects projects = baseMapper.selectById(id);
        if (projects != null && projects.getProRealStartTime() != null) {
            throw new ServiceException("已开工，不要重复操作");
        }
        Date now = DateUtil.beginOfDay(DateUtil.date());
        int status;
        if (DateUtil.beginOfDay(projects.getProPlanStartTime()).getTime() < now.getTime()) {
            status = 3; // 延迟开工
        } else if (DateUtil.beginOfDay(projects.getProPlanStartTime()).getTime() > now.getTime()) {
            status = 1; // 提前开工
        } else {
            status = 4; // 正常开工
        }
        return baseMapper.update(null, new LambdaUpdateWrapper<WpmProjects>().eq(WpmProjects::getId, id)
            .set(WpmProjects::getProStatus, status)
            .set(WpmProjects::getProRealStartTime, DateUtil.date()));
    }

    @Override
    public int proReStart(Long id) {
        WpmProjects projects = baseMapper.selectById(id);
        if (projects != null && projects.getComplateStatus() == 2) {
            throw new ServiceException("已重新开工，不要重复操作");
        }

        return baseMapper.update(null, new LambdaUpdateWrapper<WpmProjects>().eq(WpmProjects::getId, id)
            .set(WpmProjects::getComplateStatus, 2)
            .set(WpmProjects::getProRealFinishTime, null));
    }

    @Override
    @Transactional
    public int proFinish(Long id) {
        WpmProjects projects = baseMapper.selectById(id);
        if (projects != null && projects.getProRealStartTime() == null) {
            throw new ServiceException("未开工，不能完工");
        }
        if (projects != null && projects.getProRealFinishTime() != null) {
            throw new ServiceException("已完工，不要重复操作");
        }
        //修改完工状态 根据完工时间跟计划完工时间对比
        Date now = DateUtil.date();
        int status;
        if (projects.getProFinishTime().getTime() < now.getTime()) {
            status = 3; // 延迟完工
        } else if (projects.getProFinishTime().getTime() > now.getTime()) {
            status = 1; // 提前完工
        } else {
            status = 4; // 正常完工
        }
        return baseMapper.update(null, new LambdaUpdateWrapper<WpmProjects>().eq(WpmProjects::getId, id)
            .set(WpmProjects::getComplateStatus, status)
            .set(WpmProjects::getProRealFinishTime, DateUtil.date()));

    }

    @Override
    public WpmProjectsVo selectByProId(String proId) {
        return baseMapper.selectVoOne(new LambdaQueryWrapper<WpmProjects>().eq(WpmProjects::getProId, proId));
    }


}
