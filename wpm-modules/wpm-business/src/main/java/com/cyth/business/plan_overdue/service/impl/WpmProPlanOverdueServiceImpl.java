package com.cyth.business.plan_overdue.service.impl;

import com.cyth.business.patrol.domain.WpmProPatrolPic;
import com.cyth.business.plan_overdue.domain.vo.PlanOverdueFlowVo;
import com.cyth.business.project.domain.vo.WpmProjectsVo;
import com.cyth.business.project.service.IWpmProjectsService;
import com.cyth.common.core.domain.event.ProcessEvent;
import com.cyth.common.core.domain.event.ProcessTaskEvent;
import com.cyth.common.core.enums.BusinessStatusEnum;
import com.cyth.common.core.exception.ServiceException;
import com.cyth.common.core.utils.MapstructUtils;
import com.cyth.common.core.utils.StringUtils;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cyth.common.satoken.utils.LoginHelper;
import com.cyth.system.domain.SysUser;
import com.cyth.system.service.ISysUserService;
import com.cyth.workflow.common.constant.ProcessInstanceKeysConstant;
import com.cyth.workflow.domain.bo.CompleteTaskBo;
import com.cyth.workflow.domain.bo.StartProcessBo;
import com.cyth.workflow.service.IActTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import com.cyth.business.plan_overdue.domain.bo.WpmProPlanOverdueBo;
import com.cyth.business.plan_overdue.domain.vo.WpmProPlanOverdueVo;
import com.cyth.business.plan_overdue.domain.WpmProPlanOverdue;
import com.cyth.business.plan_overdue.mapper.WpmProPlanOverdueMapper;
import com.cyth.business.plan_overdue.service.IWpmProPlanOverdueService;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 项目超期Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class WpmProPlanOverdueServiceImpl implements IWpmProPlanOverdueService {

    private final WpmProPlanOverdueMapper baseMapper;

    private final IWpmProjectsService projectsService;

    private final ISysUserService userService;

    private final IActTaskService actTaskService;

    /**
     * 查询项目超期
     *
     * @param id 主键
     * @return 项目超期
     */
    @Override
    public WpmProPlanOverdueVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询项目超期列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目超期分页列表
     */
    @Override
    public TableDataInfo<WpmProPlanOverdueVo> queryPageList(WpmProPlanOverdueBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WpmProPlanOverdue> lqw = buildQueryWrapper(bo);
        Page<WpmProPlanOverdueVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的项目超期列表
     *
     * @param bo 查询条件
     * @return 项目超期列表
     */
    @Override
    public List<WpmProPlanOverdueVo> queryList(WpmProPlanOverdueBo bo) {
        LambdaQueryWrapper<WpmProPlanOverdue> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WpmProPlanOverdue> buildQueryWrapper(WpmProPlanOverdueBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WpmProPlanOverdue> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getProId()), WpmProPlanOverdue::getProId, bo.getProId());
        lqw.like(StringUtils.isNotBlank(bo.getTaskName()), WpmProPlanOverdue::getTaskName, bo.getTaskName());
        lqw.eq(bo.getPlanTaskId() != null, WpmProPlanOverdue::getPlanTaskId, bo.getPlanTaskId());
        lqw.eq(bo.getHandleId() != null, WpmProPlanOverdue::getHandleId, bo.getHandleId());
        lqw.eq(bo.getTaskId() != null, WpmProPlanOverdue::getTaskId, bo.getTaskId());
        lqw.eq(StringUtils.isNotBlank(bo.getReason()), WpmProPlanOverdue::getReason, bo.getReason());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), WpmProPlanOverdue::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增项目超期
     *
     * @param bo 项目超期
     * @return 是否新增成功
     */
    @Override
    @Transactional
    public Boolean insertByBo(WpmProPlanOverdueBo bo) {
        WpmProPlanOverdue add = MapstructUtils.convert(bo, WpmProPlanOverdue.class);
        validEntityBeforeSave(add);
        add.setStatus(BusinessStatusEnum.DRAFT.getStatus());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            //启动工作流
            //启动工作流
            StartProcessBo processBo = new StartProcessBo();
            processBo.setBusinessKey(bo.getId().toString());
            processBo.setTableName(ProcessInstanceKeysConstant.PRO_PLAN_TASK_OVER);
            Map<String, Object> variables = new HashMap<>();
            variables.put("handleUser", add.getHandleId());
            variables.put("checkUser", add.getCreateBy());
            processBo.setVariables(variables);
            Map<String, Object> res = actTaskService.startWorkFlow(processBo);
            if (!res.isEmpty()) {
                CompleteTaskBo completeTaskBo = new CompleteTaskBo();
                completeTaskBo.setTaskId(res.get("taskId").toString());
                //add.setTaskId((Long) res.get("taskId"));
                completeTaskBo.setMessageType(Collections.singletonList("1"));
                //baseMapper.updateById(add);
                actTaskService.completeTask(completeTaskBo);
            }
        }
        return flag;
    }

    /**
     * 修改项目超期
     *
     * @param bo 项目超期
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WpmProPlanOverdueBo bo) {
        WpmProPlanOverdue update = MapstructUtils.convert(bo, WpmProPlanOverdue.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WpmProPlanOverdue entity) {
        //TODO 做一些数据校验,如唯一约束
        //由部门经理发起，推送至项目负责人  由项目负责人发起，推送至任务负责人
        WpmProjectsVo projectsVo = projectsService.selectByProId(entity.getProId());
        if (!projectsVo.getProManagerId().equals(Objects.requireNonNull(LoginHelper.getLoginUser()).getEmpId())) {
            entity.setHandleId(userService.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getEmpId, projectsVo.getProManagerId())).getUserId());
        } else {
            entity.setHandleId(userService.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getEmpId, entity.getHandleEmpId())).getUserId());
        }
    }

    /**
     * 校验并批量删除项目超期信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public PlanOverdueFlowVo getFlow(Long id) {
        PlanOverdueFlowVo vo = baseMapper.getPlanOverdueFlowVoById(id);
        if (vo != null) {
            vo.setChargeUser(userService.selectUserByEmpId(vo.getChargeUserId()));
            vo.setProManager(userService.selectUserByEmpId(vo.getProManagerId()));
        }
        return vo;
    }

    @Override
    public int updateFlow(WpmProPlanOverdueBo bo) {
        WpmProPlanOverdue update = baseMapper.selectById(bo.getId());
        if (update == null) {
            throw new ServiceException("当前任务不存在");
        }
        if (!update.getStatus().equals(BusinessStatusEnum.WAITING.getStatus())) {
            throw new ServiceException("当前状态不允许修改");
        }
        if (!update.getHandleId().equals(LoginHelper.getUserId())) {
            throw new ServiceException("没有权限");
        }
        update.setReason(bo.getReason());

        return baseMapper.updateById(update);
    }

    /**
     * 总体流程监听(例如: 提交 退回 撤销 终止 作废等)
     * 正常使用只需#processEvent.key=='leave1'
     * 示例为了方便则使用startsWith匹配了全部示例key
     *
     * @param processEvent 参数
     */
    @org.springframework.context.event.EventListener(condition = "#processEvent.key.equals('" + ProcessInstanceKeysConstant.PRO_PLAN_TASK_OVER + "')")
    public void processHandler(ProcessEvent processEvent) {
        log.info("processHandler overdue 当前任务执行了{}", processEvent.toString());
        WpmProPlanOverdue overdue = baseMapper.selectById(Long.valueOf(processEvent.getBusinessKey()));
        overdue.setStatus(processEvent.getStatus());
        if (processEvent.isSubmit()) {
            overdue.setStatus(BusinessStatusEnum.WAITING.getStatus());
        }
        baseMapper.updateById(overdue);
    }

    /**
     * 执行办理任务监听
     * 示例：也可通过  @EventListener(condition = "#processTaskEvent.key=='leave1'")进行判断
     * 在方法中判断流程节点key
     * if ("xxx".equals(processTaskEvent.getTaskDefinitionKey())) {
     * //执行业务逻辑
     * }
     *
     * @param processTaskEvent 参数
     */
    //@EventListener(condition = "#processTaskEvent.key=='leave1' && #processTaskEvent.taskDefinitionKey=='Activity_14633hx'")
    @EventListener(condition = "#processTaskEvent.key=='" + ProcessInstanceKeysConstant.PRO_PLAN_TASK_OVER + "'")
    public void processTaskHandler(ProcessTaskEvent processTaskEvent) {
        log.info("processTaskHandler overdue 当前任务执行了{}", processTaskEvent.toString());
        WpmProPlanOverdue overdue = baseMapper.selectById(Long.valueOf(processTaskEvent.getBusinessKey()));
        overdue.setStatus(BusinessStatusEnum.WAITING.getStatus());
        overdue.setTaskId(Long.valueOf(processTaskEvent.getTaskId()));
        baseMapper.updateById(overdue);
    }
}
