package com.cyth.business.plan_overdue.service;

import com.cyth.business.plan_overdue.domain.vo.PlanOverdueFlowVo;
import com.cyth.business.plan_overdue.domain.vo.WpmProPlanOverdueVo;
import com.cyth.business.plan_overdue.domain.bo.WpmProPlanOverdueBo;
import com.cyth.common.mybatis.core.page.TableDataInfo;
import com.cyth.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 项目超期Service接口
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
public interface IWpmProPlanOverdueService {

    /**
     * 查询项目超期
     *
     * @param id 主键
     * @return 项目超期
     */
    WpmProPlanOverdueVo queryById(Long id);

    /**
     * 分页查询项目超期列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目超期分页列表
     */
    TableDataInfo<WpmProPlanOverdueVo> queryPageList(WpmProPlanOverdueBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的项目超期列表
     *
     * @param bo 查询条件
     * @return 项目超期列表
     */
    List<WpmProPlanOverdueVo> queryList(WpmProPlanOverdueBo bo);

    /**
     * 新增项目超期
     *
     * @param bo 项目超期
     * @return 是否新增成功
     */
    Boolean insertByBo(WpmProPlanOverdueBo bo);

    /**
     * 修改项目超期
     *
     * @param bo 项目超期
     * @return 是否修改成功
     */
    Boolean updateByBo(WpmProPlanOverdueBo bo);

    /**
     * 校验并批量删除项目超期信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    PlanOverdueFlowVo getFlow(Long id);

    int updateFlow(WpmProPlanOverdueBo bo);
}
