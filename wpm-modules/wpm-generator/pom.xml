<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.cyth</groupId>
        <artifactId>wpm-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>wpm-generator</artifactId>

    <description>
        generator 代码生成
    </description>

    <dependencies>
        <!-- 通用工具-->
        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-log</artifactId>
        </dependency>

        <!--velocity代码生成使用模板 -->
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
        </dependency>
    </dependencies>

</project>
