package com.cyth.common.translation.core.impl;

import com.cyth.common.core.service.UserService;
import com.cyth.common.translation.annotation.TranslationType;
import com.cyth.common.translation.constant.TransConstant;
import com.cyth.common.translation.core.TranslationInterface;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @version 版本号
 * @date 2024年09月27日 16:35
 * @description
 */
@AllArgsConstructor
@TranslationType(type = TransConstant.EMPLOYEE_ID_TO_NAME)
public class EmpNameTranslationImpl implements TranslationInterface<String> {
    private final UserService userService;

    @Override
    public String translation(Object key, String other) {
        if (key instanceof String id) {
            return userService.selectNicknameByEmpIds(id);
        }
        return null;
    }
}
