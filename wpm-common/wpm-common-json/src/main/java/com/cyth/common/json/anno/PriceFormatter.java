package com.cyth.common.json.anno;

import com.cyth.common.json.handler.PriceSerializer;
import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.lang.annotation.*;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @version 版本号
 * @date 2024年11月08日 18:27
 * @description
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
@JacksonAnnotationsInside
@JsonSerialize(using = PriceSerializer.class)
public @interface PriceFormatter {

    String pattern() default "0.00";

    RoundingMode mode() default RoundingMode.HALF_EVEN;

}

