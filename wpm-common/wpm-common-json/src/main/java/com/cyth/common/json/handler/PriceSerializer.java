package com.cyth.common.json.handler;

import cn.hutool.core.util.ObjectUtil;
import com.cyth.common.core.utils.NumberUtils;
import com.cyth.common.core.utils.StringUtils;
import com.cyth.common.json.anno.PriceFormatter;
import com.cyth.common.json.domain.CurrencyDecimalFormat;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @version 版本号
 * @date 2024年11月08日 18:28
 * @description
 */
@Slf4j
public class PriceSerializer  extends JsonSerializer<Object> implements ContextualSerializer {
    private String pattern;
    private RoundingMode mode;

    private static Map<String, CurrencyDecimalFormat> map = new ConcurrentHashMap<>();

    public PriceSerializer(String pattern, RoundingMode mode) {
        this.pattern = pattern;
        this.mode = mode;
    }
    public PriceSerializer() {}

    @Override
    public void serialize(Object value, JsonGenerator gen, SerializerProvider serializerProvider) throws IOException {
        DecimalFormat FORMAT = map.computeIfAbsent(pattern.concat(mode.toString()), str -> new CurrencyDecimalFormat(new DecimalFormat(pattern), mode)).getFormat();
        if (ObjectUtil.isEmpty(value)) {
            gen.writeNull();
            return;
        }
        if (value instanceof String) {
            if(pattern.equals("0.0000")){
                gen.writeString(NumberUtils.HLToYuan(Long.parseLong(value.toString())));
            }else{
                try {
                    gen.writeString(FORMAT.format(NumberUtils.divideToLong(value.toString())));
                } catch (NumberFormatException e) {
                    e.printStackTrace();
                    throw new RuntimeException("异常");
                }
            }
        } else if (value instanceof Double | value instanceof Float | value instanceof Integer | value instanceof BigDecimal | value instanceof Long) {
            log.info("value: {}", FORMAT.format(value));
            gen.writeNumber(FORMAT.format(value));
        } else {
            log.error("错误的数据类型,value: {}", value);
            throw new RuntimeException("异常");
        }
    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider serializerProvider, BeanProperty property) throws JsonMappingException {
        if (property != null) {
            PriceFormatter annotation = property.getAnnotation(PriceFormatter.class);
            if (annotation == null) {
                annotation = property.getContextAnnotation(PriceFormatter.class);
            }
            if (annotation != null) {
                return new PriceSerializer(annotation.pattern(), annotation.mode());
            }
            return serializerProvider.findValueSerializer(property.getType(), property);
        }
        return serializerProvider.findNullValueSerializer(null);
    }
}
