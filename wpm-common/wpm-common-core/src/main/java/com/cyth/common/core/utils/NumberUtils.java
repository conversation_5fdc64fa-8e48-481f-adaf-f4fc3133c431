package com.cyth.common.core.utils;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @version 版本号
 * @date 2024年11月08日 18:32
 * @description
 */
public class NumberUtils {
    public static Long parseLong(String str) {
        return StrUtil.isNotEmpty(str) ? Long.valueOf(str) : null;
    }

    public static Integer parseInt(String str) {
        return StrUtil.isNotEmpty(str) ? Integer.valueOf(str) : null;
    }

    /**
     * 通过经纬度获取地球上两点之间的距离
     *
     * 参考 <<a href="https://gitee.com/dromara/hutool/blob/1caabb586b1f95aec66a21d039c5695df5e0f4c1/hutool-core/src/main/java/cn/hutool/core/util/DistanceUtil.java">DistanceUtil</a>> 实现，目前它已经被 hutool 删除
     *
     * @param lat1 经度1
     * @param lng1 纬度1
     * @param lat2 经度2
     * @param lng2 纬度2
     * @return 距离，单位：千米
     */
    public static double getDistance(double lat1, double lng1, double lat2, double lng2) {
        double radLat1 = lat1 * Math.PI / 180.0;
        double radLat2 = lat2 * Math.PI / 180.0;
        double a = radLat1 - radLat2;
        double b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
        double distance = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2)
            + Math.cos(radLat1) * Math.cos(radLat2)
            * Math.pow(Math.sin(b / 2), 2)));
        distance = distance * 6378.137;
        distance = Math.round(distance * 10000d) / 10000d;
        return distance;
    }

    /**
     * 提供精确的乘法运算
     *
     * 和 hutool {@link NumberUtil#mul(BigDecimal...)} 的差别是，如果存在 null，则返回 null
     *
     * @param values 多个被乘值
     * @return 积
     */
    public static BigDecimal mul(BigDecimal... values) {
        for (BigDecimal value : values) {
            if (value == null) {
                return null;
            }
        }
        return NumberUtil.mul(values);
    }

    /**
     * 金额元转毫厘
     * @param yuan
     * @return
     */
    public static Long yuanToHL(Long yuan){
        return new BigDecimal(String.valueOf(yuan)).multiply(new BigDecimal("10000")).longValue();
    }

    public static Long yuanToHL(String yuan){
        return new BigDecimal(yuan).multiply(new BigDecimal("10000")).longValue();
    }
    public static String HLToYuan(Long HL){
        if (HL == null) {
            throw new IllegalArgumentException("HL cannot be null");
        }

        BigDecimal value = new BigDecimal(String.valueOf(HL));
        BigDecimal result = value.divide(new BigDecimal("10000"), 4, RoundingMode.HALF_EVEN);

        // 去除多余的零
        result = result.stripTrailingZeros();

        // 根据结果的小数位数决定保留几位小数
        int scale = result.scale();
        if (scale > 4) {
            // 如果小数位数超过4位，保留4位小数
            result = result.setScale(4, RoundingMode.HALF_EVEN);
        }

        return result.toPlainString();
        //return new BigDecimal(String.valueOf(HL)).divide(new BigDecimal("10000")).setScale(4, RoundingMode.HALF_EVEN).toString();
    }


    public static Long multiplyToLong(String num1, String num2) {
        BigDecimal mul = NumberUtil.mul(num1, num2);
        BigDecimal bigDecimal = mul.setScale(4, RoundingMode.HALF_EVEN);
        return bigDecimal.longValue();
    }

    public static String divide(Long num1) {
        return new BigDecimal(String.valueOf(num1)).divide(new BigDecimal(10000L)).toString();
    }
    public static String divideToString(String num1) {
        return new BigDecimal(num1).divide(new BigDecimal(10000L)).toString();
    }
    public static Long divideToLong(String num1) {
        //暂时不格式化
        //return new BigDecimal(num1).divide(new BigDecimal(10000L)).longValue();
        return new BigDecimal(num1).divide(new BigDecimal(1L)).longValue();
    }

}
