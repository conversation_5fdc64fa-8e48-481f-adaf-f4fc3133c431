<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>e2e-pm-platform</artifactId>
        <groupId>com.cyth</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>wpm-common-bom</module>
        <module>wpm-common-social</module>
        <module>wpm-common-core</module>
        <module>wpm-common-doc</module>
        <module>wpm-common-excel</module>
        <module>wpm-common-idempotent</module>
        <module>wpm-common-job</module>
        <module>wpm-common-log</module>
        <module>wpm-common-mail</module>
        <module>wpm-common-mybatis</module>
        <module>wpm-common-oss</module>
        <module>wpm-common-ratelimiter</module>
        <module>wpm-common-redis</module>
        <module>wpm-common-satoken</module>
        <module>wpm-common-security</module>
        <module>wpm-common-sms</module>
        <module>wpm-common-web</module>
        <module>wpm-common-translation</module>
        <module>wpm-common-sensitive</module>
        <module>wpm-common-json</module>
        <module>wpm-common-encrypt</module>
        <module>wpm-common-tenant</module>
        <module>wpm-common-websocket</module>
    </modules>

    <artifactId>wpm-common</artifactId>
    <packaging>pom</packaging>

    <description>
        common 通用模块
    </description>

</project>
