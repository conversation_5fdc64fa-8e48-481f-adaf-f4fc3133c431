<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.cyth</groupId>
        <artifactId>wpm-common</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>wpm-common-websocket</artifactId>

    <description>
        wpm-common-websocket 模块
    </description>

    <dependencies>
        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-satoken</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cyth</groupId>
            <artifactId>wpm-common-json</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
    </dependencies>
</project>
