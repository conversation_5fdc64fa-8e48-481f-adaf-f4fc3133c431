package com.cyth.web.schedule;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cyth.business.plan.domain.vo.WpmProPlanVo;
import com.cyth.business.plan.service.IWpmProPlanService;
import com.cyth.business.project.domain.vo.WpmProjectsVo;
import com.cyth.business.project.service.IWpmProjectsService;
import com.cyth.system.domain.SysNotice;
import com.cyth.system.domain.SysUser;
import com.cyth.system.domain.bo.SysNoticeBo;
import com.cyth.system.domain.vo.SysUserVo;
import com.cyth.system.service.ISysNoticeService;
import com.cyth.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 版本号
 * @date 2024年10月29日 18:11
 * @description
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ScheduledTasks {

    private final IWpmProPlanService planService;

    private final ISysNoticeService noticeService;

    private final ISysUserService userService;

    private  final IWpmProjectsService projectService;

    //@Scheduled(fixedRate = 5000) // 每5秒钟执行一次
    public  void test(){
        log.info("测试定时任务执行：" + DateUtil.date());
    }

    @Scheduled(cron = "0 15 0 * * ?") // 每天凌晨0点15分执行
    public void scheduledMethod10() {
        log.info("任务快到期10天提醒定时任务执行：" + DateUtil.date());
        List<WpmProPlanVo> list = planService.getByPlanEndTime(10);
        if (list != null) {
            for (WpmProPlanVo vo : list) {
                //判断当前时间加10天等于planEndTime
//                if (vo.getPlanEndTime() != null && DateUtil.offsetDay(DateUtil.date(), 10).compareTo(vo.getPlanEndTime()) == 0) {}
                for (String string : vo.getChargeUserId().split(",")) {
                    SysUserVo userVo = userService.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getEmpId, string));
                    SysNoticeBo noticeBo = getSysNoticeBo(vo, userVo,10);
                    noticeService.insertNotice(noticeBo);
                }
            }
        }
    }

    @NotNull
    private static SysNoticeBo getSysNoticeBo(WpmProPlanVo vo, SysUserVo userVo, int day) {
        SysNoticeBo noticeBo = new SysNoticeBo();
        noticeBo.setCreateBy(0L);
        noticeBo.setNoticeTitle(vo.getTaskName() + "任务快到期提醒");
        noticeBo.setNoticeContent("您负责的任务" + vo.getTaskName() + "快到期了(还有" + day + "天时间)，请尽快处理");
        noticeBo.setAcceptId(userVo.getUserId());
        noticeBo.setNoticeType("1");
        noticeBo.setStatus("0");
        noticeBo.setCreateDept(2L);
        noticeBo.setRemark("系统自动发送");
        return noticeBo;
    }

    @Scheduled(cron = "1 15 0 * * ?") // 每天凌晨0点15分执行
    public void scheduledMethod5() {
        log.info("任务快到期5天提醒定时任务执行：" + DateUtil.date());
        List<WpmProPlanVo> list = planService.getByPlanEndTime(5);

        if (list != null) {
            for (WpmProPlanVo vo : list) {
                //判断当前时间加10天等于planEndTime
//                if (vo.getPlanEndTime() != null && DateUtil.offsetDay(DateUtil.date(), 10).compareTo(vo.getPlanEndTime()) == 0) {}
                for (String string : vo.getChargeUserId().split(",")) {
                    SysUserVo userVo = userService.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getEmpId, string));
                    SysNoticeBo noticeBo = getSysNoticeBo(vo, userVo,5);
                    noticeService.insertNotice(noticeBo);
                }
            }
        }
    }

    @Scheduled(cron = "2 15 0 * * ?") // 每天凌晨0点15分执行
    public void scheduledMethod() {
        log.info("任务快到期提醒定时任务执行：" + DateUtil.date());
        List<WpmProPlanVo> list = planService.getByPlanEndTime(0);
        if (list != null) {
            for (WpmProPlanVo vo : list) {
                //判断当前时间加10天等于planEndTime
//                if (vo.getPlanEndTime() != null && DateUtil.offsetDay(DateUtil.date(), 10).compareTo(vo.getPlanEndTime()) == 0) {}
                //给任务负责人发消息
                for (String string : vo.getChargeUserId().split(",")) {
                    SysUserVo userVo = userService.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getEmpId, string));
                    SysNoticeBo noticeBo = getSysNoticeBo(vo, userVo,0);
                    noticeService.insertNotice(noticeBo);
                }
                //给项目负责人发消息
                WpmProjectsVo projectsVo = projectService.selectByProId(vo.getProId());
                if (projectsVo != null) {
                    SysUserVo userVo = userService.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getEmpId, projectsVo.getProManagerId()));
                    SysNoticeBo noticeBo = new SysNoticeBo();
                    noticeBo.setCreateBy(0L);
                    noticeBo.setNoticeTitle(vo.getTaskName() + "任务快到期提醒");
                    noticeBo.setNoticeContent("您负责的项目" + projectsVo.getProName() + "的任务" + vo.getTaskName() + "快到期了(还有0天时间)，请尽快处理");
                    noticeBo.setAcceptId(userVo.getUserId());
                    noticeBo.setNoticeType("1");
                    noticeBo.setStatus("0");
                    noticeBo.setCreateDept(2L);
                    noticeBo.setRemark("系统自动发送");
                    noticeService.insertNotice(noticeBo);
                }
            }
        }
    }
}
