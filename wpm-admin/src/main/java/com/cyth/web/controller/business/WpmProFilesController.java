package com.cyth.web.controller.business;

import java.io.IOException;
import java.util.List;

import cn.hutool.core.io.IoUtil;
import com.cyth.business.annotation.OnlyCreatorCanDelete;
import com.cyth.business.files.domain.bo.WpmProFilesBo;
import com.cyth.business.files.domain.vo.WpmProFilesVo;
import com.cyth.business.files.service.IWpmProFilesService;
import com.cyth.workflow.domain.bo.StartProcessBo;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.cyth.common.idempotent.annotation.RepeatSubmit;
import com.cyth.common.log.annotation.Log;
import com.cyth.common.web.core.BaseController;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.cyth.common.core.domain.R;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.log.enums.BusinessType;
import com.cyth.common.excel.utils.ExcelUtil;

import com.cyth.common.mybatis.core.page.TableDataInfo;

/**
 * 项目资料
 *
 * <AUTHOR>
 * @date 2024-09-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/proFiles")
public class WpmProFilesController extends BaseController {

    private final IWpmProFilesService wpmProFilesService;

    /**
     * 查询项目资料列表
     */
    @SaCheckPermission("business:proFiles:list")
    @GetMapping("/list")
    public TableDataInfo<WpmProFilesVo> list(WpmProFilesBo bo, PageQuery pageQuery) {
        return wpmProFilesService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出项目资料列表
     */
    @SaCheckPermission("business:proFiles:export")
    @Log(title = "项目资料", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WpmProFilesBo bo, HttpServletResponse response) {
        List<WpmProFilesVo> list = wpmProFilesService.queryList(bo);
        ExcelUtil.exportExcel(list, "项目资料", WpmProFilesVo.class, response);
    }

    /**
     * 获取项目资料详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:proFiles:query")
    @GetMapping("/{id}")
    public R<WpmProFilesVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long id) {
        return R.ok(wpmProFilesService.queryById(id));
    }

    /**
     * 新增项目资料
     */
    @SaCheckPermission("business:proFiles:add")
    @Log(title = "项目资料", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WpmProFilesBo bo) {
        return toAjax(wpmProFilesService.insertByBo(bo));
    }

    /**
     * 修改项目资料
     */
    @SaCheckPermission("business:proFiles:edit")
    @Log(title = "项目资料", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WpmProFilesBo bo) {
        return toAjax(wpmProFilesService.updateByBo(bo));
    }

    /**
     * 删除项目资料
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:proFiles:remove")
    @Log(title = "项目资料", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(wpmProFilesService.handleDelete(List.of(ids)));
    }

    /**
     * 更新项目资料目录
     *
     * @param bo
     * @return
     */
    @PutMapping("/updateIndex")
    public R<Void> updateIndex(@RequestBody WpmProFilesBo bo) {
        wpmProFilesService.updateIndex(bo);
        return R.ok();
    }

    /**
     * 更新文件类型
     *
     * @param bo
     * @return
     */
    @PutMapping("/updateFileType")
    public R<Void> updateFileType(@RequestBody WpmProFilesBo bo) {
        wpmProFilesService.updateFileType(bo);
        return R.ok();
    }

    @PutMapping("/updateFileOssid")
    public R<Void> updateFileOssid(@RequestBody WpmProFilesBo bo) {
        return toAjax(wpmProFilesService.updateFileOssid(bo));
    }

    @PutMapping("/updateRemark")
    public R<Void> updateRemark(@RequestBody WpmProFilesBo bo) {
        return toAjax(wpmProFilesService.updateRemark(bo));
    }


}
