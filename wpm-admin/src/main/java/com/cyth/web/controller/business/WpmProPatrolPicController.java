package com.cyth.web.controller.business;

import java.util.List;

import com.cyth.business.annotation.OnlyCreatorCanDelete;
import com.cyth.business.patrol.domain.WpmProPatrolPic;
import com.cyth.business.patrol.domain.bo.WpmProPatrolPicBo;
import com.cyth.business.patrol.domain.vo.WpmProPatrolPicVo;
import com.cyth.business.patrol.domain.vo.WpmProPatrolStatisticsVo;
import com.cyth.business.patrol.service.IWpmProPatrolPicService;
import com.cyth.business.plan_change.domain.bo.MonthReportBo;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.cyth.common.idempotent.annotation.RepeatSubmit;
import com.cyth.common.log.annotation.Log;
import com.cyth.common.web.core.BaseController;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.cyth.common.core.domain.R;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.log.enums.BusinessType;
import com.cyth.common.excel.utils.ExcelUtil;

import com.cyth.common.mybatis.core.page.TableDataInfo;

/**
 * 巡检图片
 *
 * <AUTHOR>
 * @date 2024-10-10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/proPatrolPic")
public class WpmProPatrolPicController extends BaseController {

    private final IWpmProPatrolPicService wpmProPatrolPicService;

    /**
     * 查询巡检图片列表
     */
    @SaCheckPermission("business:proPatrolPic:list")
    @GetMapping("/list")
    public TableDataInfo<WpmProPatrolPicVo> list(WpmProPatrolPicBo bo, PageQuery pageQuery) {
        return wpmProPatrolPicService.queryPageList(bo, pageQuery);
    }
    @SaCheckPermission("business:proPatrolPic:list")
    @GetMapping("/list/month")
    public R<List<WpmProPatrolPicVo>> getMonth(MonthReportBo bo){
        return R.ok(wpmProPatrolPicService.getMonthReport(bo));
    }

    /**
     * 导出巡检图片列表
     */
    @SaCheckPermission("business:proPatrolPic:export")
    @Log(title = "巡检图片", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WpmProPatrolPicBo bo, HttpServletResponse response) {
        List<WpmProPatrolPicVo> list = wpmProPatrolPicService.queryList(bo);
        ExcelUtil.exportExcel(list, "巡检图片", WpmProPatrolPicVo.class, response);
    }

    /**
     * 获取巡检图片详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:proPatrolPic:query")
    @GetMapping("/{id}")
    public R<WpmProPatrolPicVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(wpmProPatrolPicService.queryById(id));
    }

    /**
     * 新增巡检图片
     */
    @SaCheckPermission("business:proPatrolPic:add")
    @Log(title = "巡检图片", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WpmProPatrolPicBo bo) {
        return toAjax(wpmProPatrolPicService.insertByBo(bo));
    }

    /**
     * 修改巡检图片
     */
    @SaCheckPermission("business:proPatrolPic:edit")
    @Log(title = "巡检图片", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WpmProPatrolPicBo bo) {
        return toAjax(wpmProPatrolPicService.updateByBo(bo));
    }

    /**
     * 删除巡检图片
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:proPatrolPic:remove")
    @Log(title = "巡检图片", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @OnlyCreatorCanDelete(WpmProPatrolPic.class)
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(wpmProPatrolPicService.deleteWithValidByIds(List.of(ids), true));
    }

    @GetMapping("/getPatrolStatistics")
    public R<List<WpmProPatrolStatisticsVo>> getPatrolStatistics(MonthReportBo bo){
        return R.ok(wpmProPatrolPicService.getPatrolStatistics(bo));
    }
}
