package com.cyth.web.controller.business;

import java.util.List;

import com.cyth.business.emp.domain.WpmProEmp;
import com.cyth.business.emp.domain.bo.WpmProEmpBo;
import com.cyth.business.emp.domain.vo.WpmProEmpVo;
import com.cyth.business.emp.service.IWpmProEmpService;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.cyth.common.idempotent.annotation.RepeatSubmit;
import com.cyth.common.log.annotation.Log;
import com.cyth.common.web.core.BaseController;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.cyth.common.core.domain.R;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.log.enums.BusinessType;
import com.cyth.common.excel.utils.ExcelUtil;

import com.cyth.common.mybatis.core.page.TableDataInfo;

/**
 * 项目雇员
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/proEmp")
public class WpmProEmpController extends BaseController {

    private final IWpmProEmpService wpmProEmpService;

    /**
     * 查询项目雇员列表
     */
    @SaCheckPermission("business:proEmp:list")
    @GetMapping("/list")
    public TableDataInfo<WpmProEmpVo> list(WpmProEmpBo bo, PageQuery pageQuery) {
        return wpmProEmpService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出项目雇员列表
     */
    @SaCheckPermission("business:proEmp:export")
    @Log(title = "项目雇员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WpmProEmpBo bo, HttpServletResponse response) {
        List<WpmProEmpVo> list = wpmProEmpService.queryList(bo);
        ExcelUtil.exportExcel(list, "项目雇员", WpmProEmpVo.class, response);
    }

    /**
     * 获取项目雇员详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:proEmp:query")
    @GetMapping("/{id}")
    public R<WpmProEmpVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(wpmProEmpService.queryById(id));
    }

    /**
     * 新增项目雇员
     */
    @SaCheckPermission("business:proEmp:add")
    @Log(title = "项目雇员", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WpmProEmpBo bo) {
        return toAjax(wpmProEmpService.insertByBo(bo));
    }

    /**
     * 修改项目雇员
     */
    @SaCheckPermission("business:proEmp:edit")
    @Log(title = "项目雇员", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WpmProEmpBo bo) {
        return toAjax(wpmProEmpService.updateByBo(bo));
    }

    /**
     * 删除项目雇员
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:proEmp:remove")
    @Log(title = "项目雇员", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(wpmProEmpService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 从noma同步数据
     * @return
     */
    @SaCheckPermission("business:project:syncFromNoma")
    @Log(title = "项目雇员", businessType = BusinessType.OTHER)
    @GetMapping("/syncFromNoma/{proId}")
    public R<Void> syncFromNoma(@NotBlank(message = "项目id不能为空")
                                    @PathVariable  String proId){
        return toAjax(wpmProEmpService.syncFromNoma(proId));
    }

    @GetMapping("/getUserByProId/{proId}")
    public R<List<WpmProEmpVo>> getUserByProId(@NotBlank(message = "项目id不能为空")
                                                  @PathVariable  String proId){
        return R.ok(wpmProEmpService.getUserByProId(proId));
    }

    /**
     * 撤场操作
     * @param bo
     * @return
     */
    @SaCheckPermission("business:proEmp:add")
    @PostMapping("/updateProEmpStatus")
    public R<Void> updateProEmpStatus(@RequestBody WpmProEmpBo bo){
        return toAjax(wpmProEmpService.updateProEmpStatus(bo));
    }
}
