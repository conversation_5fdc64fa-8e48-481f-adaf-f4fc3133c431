package com.cyth.web.controller.business;

import java.util.List;
import java.util.Objects;

import com.cyth.business.project.domain.bo.WpmProjectsBo;
import com.cyth.business.project.domain.vo.WpmProjectsVo;
import com.cyth.business.project.service.IWpmProjectsService;
import com.cyth.common.satoken.utils.LoginHelper;
import com.cyth.common.tenant.helper.TenantHelper;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.cyth.common.idempotent.annotation.RepeatSubmit;
import com.cyth.common.log.annotation.Log;
import com.cyth.common.web.core.BaseController;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.cyth.common.core.domain.R;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.log.enums.BusinessType;
import com.cyth.common.excel.utils.ExcelUtil;

import com.cyth.common.mybatis.core.page.TableDataInfo;

/**
 * 项目
 *
 * <AUTHOR>
 * @date 2024-09-13
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/projects")
public class WpmProjectsController extends BaseController {

    private final IWpmProjectsService wpmProjectsService;

    /**
     * 查询项目列表
     */
    @SaCheckPermission("business:projects:list")
    @GetMapping("/list")
    public TableDataInfo<WpmProjectsVo> list(WpmProjectsBo bo, PageQuery pageQuery) {
//        if(LoginHelper.isSuperAdmin()|| Objects.requireNonNull(LoginHelper.getLoginUser()).getRolePermission().contains("globalManager")){
//            wpmProjectsService.syncFromNoma(bo,pageQuery);
//        }
        return wpmProjectsService.queryPageList(bo, pageQuery);
    }

    @GetMapping("/list/kb")
    public TableDataInfo<WpmProjectsVo> listForKB(WpmProjectsBo bo, PageQuery pageQuery) {

        return wpmProjectsService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出项目列表
     */
    @SaCheckPermission("business:projects:export")
    @Log(title = "项目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WpmProjectsBo bo, HttpServletResponse response) {
        List<WpmProjectsVo> list = wpmProjectsService.queryList(bo);
        ExcelUtil.exportExcel(list, "项目", WpmProjectsVo.class, response);
    }

    /**
     * 获取项目详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:projects:query")
    @GetMapping("/{id}")
    public R<WpmProjectsVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(wpmProjectsService.queryById(id));
    }

    @SaCheckPermission("business:projects:query")
    @GetMapping("/proId/{proId}")
    public R<WpmProjectsVo> getInfoByProId(@NotNull(message = "主键不能为空")
                                    @PathVariable String proId) {
        return R.ok(wpmProjectsService.selectByProId(proId));
    }

    /**
     * 新增项目
     */
    @SaCheckPermission("business:projects:add")
    @Log(title = "项目", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WpmProjectsBo bo) {
        return toAjax(wpmProjectsService.insertByBo(bo));
    }

    /**
     * 修改项目
     */
    @SaCheckPermission("business:projects:edit")
    @Log(title = "项目", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WpmProjectsBo bo) {
        return toAjax(wpmProjectsService.updateByBo(bo));
    }

    @SaCheckPermission("business:projects:edit")
    @Log(title = "项目", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/price")
    public R<Void> editPrice(@Validated(EditGroup.class) @RequestBody WpmProjectsBo bo) {
        return toAjax(wpmProjectsService.updateByBo(bo));
    }

    /**
     * 删除项目
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:projects:remove")
    @Log(title = "项目", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(wpmProjectsService.deleteWithValidByIds(List.of(ids), true));
    }

    //从诺明系统同步项目
    @SaCheckPermission("business:project:syncFromNoma")
    @GetMapping("/syncFromNoma")
    public TableDataInfo<WpmProjectsVo> syncFromNoma(WpmProjectsBo bo, PageQuery pageQuery) {
        //return toAjax(TenantHelper.ignore(()->wpmProjectsService.syncFromNoma()));
        return wpmProjectsService.syncFromNoma(bo,pageQuery);
    }

    @GetMapping("/tree")
    public R<List<WpmProjectsVo>> queryList(WpmProjectsBo bo) {
        return R.ok(wpmProjectsService.queryList(bo));
    }

    /**
     * 计划变更
     * @param bo
     * @return
     */
    @SaCheckPermission("business:projects:change")
    @PutMapping("/updateStatus")
    public R<Void> updateProjectStatus(@RequestBody WpmProjectsBo bo) {
        return toAjax(wpmProjectsService.updateProjectStatus(bo));
    }

    /**
     * 开工
     * @param id
     * @return
     */
    @SaCheckPermission("business:projects:start")
    @GetMapping({"/proStart/{id}"})
    public R<Void> proStart(@PathVariable("id") Long id) {
        return toAjax(wpmProjectsService.proStart(id));
    }

    /**
     * 重新开工
     * @param id
     * @return
     */
    @SaCheckPermission("business:projects:start")
    @GetMapping({"/proReStart/{id}"})
    public R<Void> proReStart(@PathVariable("id") Long id) {
        return toAjax(wpmProjectsService.proReStart(id));
    }

    /**
     * 完工
     * @param id
     * @return
     */
    @SaCheckPermission("business:projects:finish")
    @GetMapping({"/proFinish/{id}"})
    public R<Void> proFinish(@PathVariable("id") Long id) {
        return toAjax(wpmProjectsService.proFinish(id));
    }
}
