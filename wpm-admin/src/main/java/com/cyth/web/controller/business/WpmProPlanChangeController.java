package com.cyth.web.controller.business;

import java.util.List;

import com.cyth.business.plan_change.domain.bo.WpmProPlanChangeBo;
import com.cyth.business.plan_change.domain.bo.MonthReportBo;
import com.cyth.business.plan_change.domain.vo.WpmProPlanChangeVo;
import com.cyth.business.plan_change.service.IWpmProPlanChangeService;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.cyth.common.idempotent.annotation.RepeatSubmit;
import com.cyth.common.log.annotation.Log;
import com.cyth.common.web.core.BaseController;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.cyth.common.core.domain.R;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.log.enums.BusinessType;
import com.cyth.common.excel.utils.ExcelUtil;

import com.cyth.common.mybatis.core.page.TableDataInfo;

/**
 * 项目计划变更
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/proPlanChange")
public class WpmProPlanChangeController extends BaseController {

    private final IWpmProPlanChangeService wpmProPlanChangeService;

    /**
     * 查询项目计划变更列表
     */
    @SaCheckPermission("business:proPlanChange:list")
    @GetMapping("/list")
    public TableDataInfo<WpmProPlanChangeVo> list(WpmProPlanChangeBo bo, PageQuery pageQuery) {
        return wpmProPlanChangeService.queryPageList(bo, pageQuery);
    }

    @SaCheckPermission("business:proPlanChange:list")
    @GetMapping("/list/month")
    public R<List<WpmProPlanChangeVo>> getProPlanChangeList(MonthReportBo bo) {
        return R.ok(wpmProPlanChangeService.queryListByProId(bo));
    }

    /**
     * 导出项目计划变更列表
     */
    @SaCheckPermission("business:proPlanChange:export")
    @Log(title = "项目计划变更", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WpmProPlanChangeBo bo, HttpServletResponse response) {
        List<WpmProPlanChangeVo> list = wpmProPlanChangeService.queryList(bo);
        ExcelUtil.exportExcel(list, "项目计划变更", WpmProPlanChangeVo.class, response);
    }

    /**
     * 获取项目计划变更详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:proPlanChange:query")
    @GetMapping("/{id}")
    public R<WpmProPlanChangeVo> getInfo(@NotNull(message = "主键不能为空")
                                         @PathVariable Long id) {
        return R.ok(wpmProPlanChangeService.queryById(id));
    }

    /**
     * 新增项目计划变更
     */
    @SaCheckPermission("business:proPlanChange:add")
    @Log(title = "项目计划变更", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WpmProPlanChangeBo bo) {
        return toAjax(wpmProPlanChangeService.insertByBo(bo));
    }

    /**
     * 修改项目计划变更
     */
    @SaCheckPermission("business:proPlanChange:edit")
    @Log(title = "项目计划变更", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WpmProPlanChangeBo bo) {
        return toAjax(wpmProPlanChangeService.updateByBo(bo));
    }

    /**
     * 删除项目计划变更
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:proPlanChange:remove")
    @Log(title = "项目计划变更", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(wpmProPlanChangeService.deleteWithValidByIds(List.of(ids), true));
    }
}
