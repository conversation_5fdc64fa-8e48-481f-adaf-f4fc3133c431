package com.cyth.web.controller.business;

import java.util.List;

import com.cyth.business.plan_change.domain.bo.MonthReportBo;
import com.cyth.business.pro_pay.domain.bo.WpmProPaymentInfoBo;
import com.cyth.business.pro_pay.domain.vo.PaymentInfoMonthVo;
import com.cyth.business.pro_pay.domain.vo.WpmProPaymentInfoVo;
import com.cyth.business.pro_pay.service.IWpmProPaymentInfoService;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.cyth.common.idempotent.annotation.RepeatSubmit;
import com.cyth.common.log.annotation.Log;
import com.cyth.common.web.core.BaseController;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.cyth.common.core.domain.R;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.log.enums.BusinessType;
import com.cyth.common.excel.utils.ExcelUtil;

import com.cyth.common.mybatis.core.page.TableDataInfo;

/**
 * 项目付款信息
 *
 * <AUTHOR>
 * @date 2024-11-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/proPaymentInfo")
public class WpmProPaymentInfoController extends BaseController {

    private final IWpmProPaymentInfoService wpmProPaymentInfoService;

    /**
     * 查询项目付款信息列表
     */
    @SaCheckPermission("business:proPaymentInfo:list")
    @GetMapping("/list")
    public TableDataInfo<WpmProPaymentInfoVo> list(WpmProPaymentInfoBo bo, PageQuery pageQuery) {
        return wpmProPaymentInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出项目付款信息列表
     */
    @SaCheckPermission("business:proPaymentInfo:export")
    @Log(title = "项目付款信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WpmProPaymentInfoBo bo, HttpServletResponse response) {
        List<WpmProPaymentInfoVo> list = wpmProPaymentInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "项目付款信息", WpmProPaymentInfoVo.class, response);
    }

    /**
     * 获取项目付款信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:proPaymentInfo:query")
    @GetMapping("/{id}")
    public R<WpmProPaymentInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(wpmProPaymentInfoService.queryById(id));
    }

    /**
     * 新增项目付款信息
     */
    @SaCheckPermission("business:proPaymentInfo:add")
    @Log(title = "项目付款信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WpmProPaymentInfoBo bo) {
        return toAjax(wpmProPaymentInfoService.insertByBo(bo));
    }

    /**
     * 修改项目付款信息
     */
    @SaCheckPermission("business:proPaymentInfo:edit")
    @Log(title = "项目付款信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WpmProPaymentInfoBo bo) {
        return toAjax(wpmProPaymentInfoService.updateByBo(bo));
    }

    /**
     * 删除项目付款信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:proPaymentInfo:remove")
    @Log(title = "项目付款信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(wpmProPaymentInfoService.deleteWithValidByIds(List.of(ids), true));
    }
    @GetMapping("/monthReport")
    public R<PaymentInfoMonthVo> getPaymentInfoMonth(@Validated(AddGroup.class) MonthReportBo bo){
        return R.ok(wpmProPaymentInfoService.queryPaymentInfoMonth(bo));
    }
}
