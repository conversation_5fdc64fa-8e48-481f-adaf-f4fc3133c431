package com.cyth.web.controller.business;

import java.io.IOException;
import java.util.List;

import cn.hutool.core.io.IoUtil;
import com.cyth.business.annotation.CheckTaskStatus;
import com.cyth.business.annotation.OnlyCreatorCanDelete;
import com.cyth.business.file_upload.domain.WpmProFilesUpload;
import com.cyth.business.file_upload.domain.bo.WpmProFilesUploadBo;
import com.cyth.business.file_upload.domain.vo.WpmProFilesUploadVo;
import com.cyth.business.file_upload.service.IWpmProFilesUploadService;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.cyth.common.idempotent.annotation.RepeatSubmit;
import com.cyth.common.log.annotation.Log;
import com.cyth.common.web.core.BaseController;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.cyth.common.core.domain.R;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.log.enums.BusinessType;
import com.cyth.common.excel.utils.ExcelUtil;

import com.cyth.common.mybatis.core.page.TableDataInfo;

/**
 * 项目资料上传
 *
 * <AUTHOR>
 * @date 2024-10-16
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/proFilesUpload")
public class WpmProFilesUploadController extends BaseController {

    private final IWpmProFilesUploadService wpmProFilesUploadService;

    /**
     * 查询项目资料上传列表
     */
    @SaCheckPermission("business:proFilesUpload:list")
    @GetMapping("/list")
    public TableDataInfo<WpmProFilesUploadVo> list(WpmProFilesUploadBo bo, PageQuery pageQuery) {
        return wpmProFilesUploadService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出项目资料上传列表
     */
    @SaCheckPermission("business:proFilesUpload:export")
    @Log(title = "项目资料上传", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WpmProFilesUploadBo bo, HttpServletResponse response) {
        List<WpmProFilesUploadVo> list = wpmProFilesUploadService.queryList(bo);
        ExcelUtil.exportExcel(list, "项目资料上传", WpmProFilesUploadVo.class, response);
    }

    /**
     * 获取项目资料上传详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:proFilesUpload:query")
    @GetMapping("/{id}")
    public R<WpmProFilesUploadVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(wpmProFilesUploadService.queryById(id));
    }

    /**
     * 新增项目资料上传
     */
    @SaCheckPermission("business:proFilesUpload:add")
    @Log(title = "项目资料上传", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WpmProFilesUploadBo bo) {
        return toAjax(wpmProFilesUploadService.insertByBo(bo));
    }

    /**
     * 修改项目资料上传
     */
    @SaCheckPermission("business:proFilesUpload:edit")
    @Log(title = "项目资料上传", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WpmProFilesUploadBo bo) {
        return toAjax(wpmProFilesUploadService.updateByBo(bo));
    }

    /**
     * 删除项目资料上传
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:proFilesUpload:remove")
    @Log(title = "项目资料上传", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @OnlyCreatorCanDelete(WpmProFilesUpload.class)
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(wpmProFilesUploadService.deleteWithValidByIds(List.of(ids), true));
    }

    @Log(title = "项目资料", businessType = BusinessType.DOWNLOAD)
    @GetMapping("/batchDownload/{ids}")
    public void batchDowload(@PathVariable Long[] ids, HttpServletResponse response) throws IOException {
        byte[] data = wpmProFilesUploadService.batchDownload(List.of(ids));
        response.reset();
        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-Disposition", "attachment; filename=\"wpm.zip\"");
        response.addHeader("Content-Length", "" + data.length);
        response.setContentType("application/octet-stream; charset=UTF-8");
        IoUtil.write(response.getOutputStream(), false, data);
    }
}
