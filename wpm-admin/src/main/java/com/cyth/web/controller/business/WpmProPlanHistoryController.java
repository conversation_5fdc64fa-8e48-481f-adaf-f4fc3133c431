package com.cyth.web.controller.business;

import java.util.List;

import com.cyth.business.plan_history.domain.bo.WpmProPlanHistoryBo;
import com.cyth.business.plan_history.domain.vo.WpmProPlanHistoryVo;
import com.cyth.business.plan_history.service.IWpmProPlanHistoryService;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.cyth.common.idempotent.annotation.RepeatSubmit;
import com.cyth.common.log.annotation.Log;
import com.cyth.common.web.core.BaseController;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.cyth.common.core.domain.R;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.log.enums.BusinessType;
import com.cyth.common.excel.utils.ExcelUtil;

import com.cyth.common.mybatis.core.page.TableDataInfo;

/**
 * 项目计划历史
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/proPlanHistory")
public class WpmProPlanHistoryController extends BaseController {

    private final IWpmProPlanHistoryService wpmProPlanHistoryService;

    /**
     * 查询项目计划历史列表
     */
    @SaCheckPermission("business:proPlanHistory:list")
    @GetMapping("/list")
    public TableDataInfo<WpmProPlanHistoryVo> list(WpmProPlanHistoryBo bo, PageQuery pageQuery) {
        return wpmProPlanHistoryService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出项目计划历史列表
     */
    @SaCheckPermission("business:proPlanHistory:export")
    @Log(title = "项目计划历史", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WpmProPlanHistoryBo bo, HttpServletResponse response) {
        List<WpmProPlanHistoryVo> list = wpmProPlanHistoryService.queryList(bo);
        ExcelUtil.exportExcel(list, "项目计划历史", WpmProPlanHistoryVo.class, response);
    }

    /**
     * 获取项目计划历史详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:proPlanHistory:query")
    @GetMapping("/{id}")
    public R<WpmProPlanHistoryVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(wpmProPlanHistoryService.queryById(id));
    }

    /**
     * 新增项目计划历史
     */
    @SaCheckPermission("business:proPlanHistory:add")
    @Log(title = "项目计划历史", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WpmProPlanHistoryBo bo) {
        return toAjax(wpmProPlanHistoryService.insertByBo(bo));
    }

    /**
     * 修改项目计划历史
     */
    @SaCheckPermission("business:proPlanHistory:edit")
    @Log(title = "项目计划历史", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WpmProPlanHistoryBo bo) {
        return toAjax(wpmProPlanHistoryService.updateByBo(bo));
    }

    /**
     * 删除项目计划历史
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:proPlanHistory:remove")
    @Log(title = "项目计划历史", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(wpmProPlanHistoryService.deleteWithValidByIds(List.of(ids), true));
    }
}
