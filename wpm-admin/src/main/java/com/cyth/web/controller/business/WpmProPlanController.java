package com.cyth.web.controller.business;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.date.DateUtil;
import com.cyth.business.annotation.CheckTaskStatus;
import com.cyth.business.plan.domain.bo.WpmProPlanBo;
import com.cyth.business.plan.domain.vo.StatisticsPlanResultVo;
import com.cyth.business.plan.domain.vo.StatisticsPlanVo;
import com.cyth.business.plan.domain.vo.WpmProPlanVo;
import com.cyth.business.plan.service.IWpmProPlanService;
import com.cyth.business.plan_change.domain.bo.MonthReportBo;
import com.cyth.business.tasks.domain.bo.WpmPlanTasksBo;
import com.cyth.business.tasks.domain.vo.TaskTreeVo;
import com.cyth.business.tasks.domain.vo.WpmPlanTasksVo;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import oracle.sql.DATE;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.cyth.common.idempotent.annotation.RepeatSubmit;
import com.cyth.common.log.annotation.Log;
import com.cyth.common.web.core.BaseController;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.cyth.common.core.domain.R;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.log.enums.BusinessType;
import com.cyth.common.excel.utils.ExcelUtil;

import com.cyth.common.mybatis.core.page.TableDataInfo;

/**
 * 项目计划
 *
 * <AUTHOR>
 * @date 2024-09-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/proPlan")
public class WpmProPlanController extends BaseController {

    private final IWpmProPlanService wpmProPlanService;

    /**
     * 查询项目计划列表
     */
    @SaCheckPermission("business:proPlan:list")
    @GetMapping("/list")
    public R<List<WpmProPlanVo>> list(WpmProPlanBo bo) {
        return R.ok(wpmProPlanService.queryList(bo));
    }

    @SaCheckPermission("business:proPatrolPic:list")
    @GetMapping("/listRecord")
    public R<List<WpmProPlanVo>> listRecord(WpmProPlanBo bo) {
        return R.ok(wpmProPlanService.queryList(bo));
    }

    @GetMapping("/listRecordId")
    public R<List<Long>> listRecordId(WpmProPlanBo bo) {
        return R.ok(wpmProPlanService.queryList(bo).stream().map(WpmProPlanVo::getId).collect(Collectors.toList()));
    }


    @SaCheckPermission("business:proPlan:currentManage")
    @GetMapping("/currentManage")
    public R<List<WpmProPlanVo>> currentManage(WpmProPlanBo bo) {
        return R.ok(wpmProPlanService.currentManage(bo));
    }


    /**
     * 导出项目计划列表
     */
    @SaCheckPermission("business:proPlan:export")
    @Log(title = "项目计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WpmProPlanBo bo, HttpServletResponse response) {
        List<WpmProPlanVo> list = wpmProPlanService.queryList(bo);
        ExcelUtil.exportExcel(list, "项目计划", WpmProPlanVo.class, response);
    }

    /**
     * 获取项目计划详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:proPlan:query")
    @GetMapping("/{id}")
    public R<WpmProPlanVo> getInfo(@NotNull(message = "主键不能为空")
                                   @PathVariable Long id) {
        return R.ok(wpmProPlanService.queryById(id));
    }

    /**
     * 新增项目计划
     */
    @SaCheckPermission("business:proPlan:add")
    @Log(title = "项目计划", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WpmProPlanBo bo) {
        return toAjax(wpmProPlanService.savePlan(bo));
    }

    /**
     * 修改项目计划
     */
    @SaCheckPermission("business:proPlan:edit")
    @Log(title = "项目计划", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WpmProPlanBo bo) {
        return toAjax(wpmProPlanService.updateByBo(bo));
    }

    /**
     * 删除项目计划
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:proPlan:remove")
    @Log(title = "项目计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(wpmProPlanService.deleteWithValidByIds(List.of(ids), true));
    }

    @SaCheckPermission("business:proPlan:edit")
    @Log(title = "项目计划", businessType = BusinessType.UPDATE)
    @DeleteMapping("/enable/{ids}")
    public R<Void> enable(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(wpmProPlanService.enable(List.of(ids), true));
    }

    /**
     * 修改项目计划结束时间
     */
    @RepeatSubmit()
    @PutMapping("/changeEndTime")
    @SaCheckPermission("business:proPlanRecord:edit")
    //@CheckTaskStatus(WpmProPlanBo.class)
    public R<Void> changeEndTime(@Validated(EditGroup.class) @RequestBody WpmProPlanBo bo) {
        return toAjax(wpmProPlanService.changeEndTime(bo));
    }

    /**
     * 修改项目计划开始时间
     */
    @RepeatSubmit()
    @PutMapping("/changeStartTime")
    @SaCheckPermission("business:proPlanRecord:edit")
    public R<Void> changeStartTime(@Validated(EditGroup.class) @RequestBody WpmProPlanBo bo) {
        return toAjax(wpmProPlanService.changeStartTime(bo));
    }

    /**
     * 修改项目计划负责人
     */
    @RepeatSubmit
    @SaCheckPermission("business:proPlanRecord:edit")
    @PutMapping("/changeChargePerson")
    //@CheckTaskStatus(WpmProPlanBo.class)
    public R<Void> changeChargePerson(@Validated(EditGroup.class) @RequestBody WpmProPlanBo bo) {
        return toAjax(wpmProPlanService.changeChargePerson(bo));
    }

    /**
     * 修改项目计划外部人
     */
    @RepeatSubmit
    @SaCheckPermission("business:proPlanRecord:edit")
    @PutMapping("/changeExternalUserId")
    //@CheckTaskStatus(WpmProPlanBo.class)
    public R<Void> changeExternalUserId(@Validated(EditGroup.class) @RequestBody WpmProPlanBo bo) {
        return toAjax(wpmProPlanService.changeExternalUserId(bo));
    }

    /**
     * 开工
     *
     * @param bo
     * @return
     */
    @SaCheckPermission("business:proPlanRecord:start")
    @PutMapping("/startPlan")
    @CheckTaskStatus(WpmProPlanBo.class)
    public R<Void> startPlan(@RequestBody WpmProPlanBo bo) {
        return toAjax(wpmProPlanService.startPlan(bo));
    }

    /**
     * 完工
     *
     * @param bo
     * @return
     */
    @SaCheckPermission("business:proPlanRecord:complete")
    @PutMapping("/complatePlan")
    @CheckTaskStatus(WpmProPlanBo.class)
    public R<Void> complatePlan(@RequestBody WpmProPlanBo bo) {
        return toAjax(wpmProPlanService.complatePlan(bo));
    }

    @GetMapping("/treeselect")
    public R<TaskTreeVo> treeselect(WpmProPlanBo tasksBo) {
        return R.ok(wpmProPlanService.initTreeSelect(tasksBo));
    }

    @GetMapping("/queryStatisticsPlan/{proId}")
    public R<StatisticsPlanResultVo> queryStatisticsPlan(@NotBlank(message = "请选择项目") @PathVariable String proId) {
        List<StatisticsPlanVo> planVos = wpmProPlanService.queryStatisticsPlan(proId);
        StatisticsPlanResultVo resultVo = new StatisticsPlanResultVo();
        resultVo.setList(planVos);
        resultVo.setMinDay(planVos.stream().map(StatisticsPlanVo::getStartDate).filter(Objects::nonNull).min(Date::compareTo).orElse(DateUtil.date()));
        resultVo.setMaxDay(planVos.stream().map(StatisticsPlanVo::getEndDate).filter(Objects::nonNull).max(Date::compareTo).orElse(DateUtil.date()));
        return R.ok(resultVo);
    }

    @GetMapping("/waitingTask/{proId}")
    public R<List<WpmProPlanVo>> waitingTask(MonthReportBo bo) {
        return R.ok(wpmProPlanService.waitingTask(bo));
    }

    @GetMapping("/monthReport")
    public R<List<WpmProPlanVo>> monthReport(@Validated(AddGroup.class) MonthReportBo bo) {

        return R.ok(wpmProPlanService.monthReport(bo));
    }
}
