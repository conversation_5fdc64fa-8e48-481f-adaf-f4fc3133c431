package com.cyth.web.controller.business;

import java.util.List;

import com.cyth.business.file_index.domain.bo.WpmProDocumentIndexBo;
import com.cyth.business.file_index.domain.vo.WpmProDocumentIndexVo;
import com.cyth.business.file_index.service.IWpmProDocumentIndexService;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.cyth.common.idempotent.annotation.RepeatSubmit;
import com.cyth.common.log.annotation.Log;
import com.cyth.common.web.core.BaseController;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.cyth.common.core.domain.R;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.log.enums.BusinessType;
import com.cyth.common.excel.utils.ExcelUtil;

import com.cyth.common.mybatis.core.page.TableDataInfo;

/**
 * 项目资料目录
 *
 * <AUTHOR>
 * @date 2024-10-16
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/proDocumentIndex")
public class WpmProDocumentIndexController extends BaseController {

    private final IWpmProDocumentIndexService wpmProDocumentIndexService;

    /**
     * 查询项目资料目录列表
     */
    @SaCheckPermission("business:proDocumentIndex:list")
    @GetMapping("/list")
    public R<List<WpmProDocumentIndexVo>> list(WpmProDocumentIndexBo bo, PageQuery pageQuery) {
        return R.ok(wpmProDocumentIndexService.queryList(bo));
    }

    /**
     * 导出项目资料目录列表
     */
    @SaCheckPermission("business:proDocumentIndex:export")
    @Log(title = "项目资料目录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WpmProDocumentIndexBo bo, HttpServletResponse response) {
        List<WpmProDocumentIndexVo> list = wpmProDocumentIndexService.queryList(bo);
        ExcelUtil.exportExcel(list, "项目资料目录", WpmProDocumentIndexVo.class, response);
    }

    /**
     * 获取项目资料目录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:proDocumentIndex:query")
    @GetMapping("/{id}")
    public R<WpmProDocumentIndexVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(wpmProDocumentIndexService.queryById(id));
    }

    /**
     * 新增项目资料目录
     */
    @SaCheckPermission("business:proDocumentIndex:add")
    @Log(title = "项目资料目录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WpmProDocumentIndexBo bo) {
        return toAjax(wpmProDocumentIndexService.insertByBo(bo));
    }

    /**
     * 修改项目资料目录
     */
    @SaCheckPermission("business:proDocumentIndex:edit")
    @Log(title = "项目资料目录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WpmProDocumentIndexBo bo) {
        return toAjax(wpmProDocumentIndexService.updateByBo(bo));
    }

    /**
     * 删除项目资料目录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:proDocumentIndex:remove")
    @Log(title = "项目资料目录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(wpmProDocumentIndexService.deleteWithValidByIds(List.of(ids), true));
    }
}
