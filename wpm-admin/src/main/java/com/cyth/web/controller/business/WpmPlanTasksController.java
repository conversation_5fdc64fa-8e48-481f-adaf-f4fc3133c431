package com.cyth.web.controller.business;

import java.util.List;

import cn.hutool.core.lang.tree.Tree;

import com.cyth.business.tasks.domain.bo.WpmPlanTasksBo;
import com.cyth.business.tasks.domain.vo.TaskTreeVo;
import com.cyth.business.tasks.domain.vo.WpmPlanTasksVo;
import com.cyth.business.tasks.service.IWpmPlanTasksService;
import com.cyth.common.satoken.utils.LoginHelper;
import com.cyth.system.domain.bo.SysMenuBo;
import com.cyth.system.domain.vo.SysMenuVo;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.cyth.common.idempotent.annotation.RepeatSubmit;
import com.cyth.common.log.annotation.Log;
import com.cyth.common.web.core.BaseController;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.cyth.common.core.domain.R;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.log.enums.BusinessType;
import com.cyth.common.excel.utils.ExcelUtil;

import com.cyth.common.mybatis.core.page.TableDataInfo;

/**
 * 任务
 *
 * <AUTHOR>
 * @date 2024-09-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/planTasks")
public class WpmPlanTasksController extends BaseController {

    private final IWpmPlanTasksService wpmplanTasksService;
    /**
     * 查询任务列表
     */
    @SaCheckPermission("business:planTasks:list")
    @GetMapping("/list")
    public R<List<WpmPlanTasksVo>> list(WpmPlanTasksBo bo) {
        return R.ok(wpmplanTasksService.queryList(bo));
    }

    /**
     * 导出任务列表
     */
    @SaCheckPermission("business:planTasks:export")
    @Log(title = "任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WpmPlanTasksBo bo, HttpServletResponse response) {
        List<WpmPlanTasksVo> list = wpmplanTasksService.queryList(bo);
        ExcelUtil.exportExcel(list, "任务", WpmPlanTasksVo.class, response);
    }

    /**
     * 获取任务详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:planTasks:query")
    @GetMapping("/{id}")
    public R<WpmPlanTasksVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(wpmplanTasksService.queryById(id));
    }

    /**
     * 新增任务
     */
    @SaCheckPermission("business:planTasks:add")
    @Log(title = "任务", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WpmPlanTasksBo bo) {
        return toAjax(wpmplanTasksService.insertByBo(bo));
    }

    /**
     * 修改任务
     */
    @SaCheckPermission("business:planTasks:edit")
    @Log(title = "任务", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WpmPlanTasksBo bo) {
        return toAjax(wpmplanTasksService.updateByBo(bo));
    }

    /**
     * 删除任务
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:planTasks:remove")
    @Log(title = "任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(wpmplanTasksService.deleteWithValidByIds(List.of(ids), true));
    }


}
