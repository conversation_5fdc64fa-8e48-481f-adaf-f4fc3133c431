package com.cyth.web.controller.business;

import java.util.List;

import com.cyth.business.plan_overdue.domain.vo.PlanOverdueFlowVo;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.cyth.common.idempotent.annotation.RepeatSubmit;
import com.cyth.common.log.annotation.Log;
import com.cyth.common.web.core.BaseController;
import com.cyth.common.mybatis.core.page.PageQuery;
import com.cyth.common.core.domain.R;
import com.cyth.common.core.validate.AddGroup;
import com.cyth.common.core.validate.EditGroup;
import com.cyth.common.log.enums.BusinessType;
import com.cyth.common.excel.utils.ExcelUtil;
import com.cyth.business.plan_overdue.domain.vo.WpmProPlanOverdueVo;
import com.cyth.business.plan_overdue.domain.bo.WpmProPlanOverdueBo;
import com.cyth.business.plan_overdue.service.IWpmProPlanOverdueService;
import com.cyth.common.mybatis.core.page.TableDataInfo;

/**
 * 项目超期
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/proPlanOverdue")
public class WpmProPlanOverdueController extends BaseController {

    private final IWpmProPlanOverdueService wpmProPlanOverdueService;

    /**
     * 查询项目超期列表
     */
    @SaCheckPermission("business:proPlanOverdue:list")
    @GetMapping("/list")
    public TableDataInfo<WpmProPlanOverdueVo> list(WpmProPlanOverdueBo bo, PageQuery pageQuery) {
        return wpmProPlanOverdueService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出项目超期列表
     */
    @SaCheckPermission("business:proPlanOverdue:export")
    @Log(title = "项目超期", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WpmProPlanOverdueBo bo, HttpServletResponse response) {
        List<WpmProPlanOverdueVo> list = wpmProPlanOverdueService.queryList(bo);
        ExcelUtil.exportExcel(list, "项目超期", WpmProPlanOverdueVo.class, response);
    }

    /**
     * 获取项目超期详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:proPlanOverdue:query")
    @GetMapping("/{id}")
    public R<WpmProPlanOverdueVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(wpmProPlanOverdueService.queryById(id));
    }

    /**
     * 新增项目超期
     */
    @SaCheckPermission("business:proPlanOverdue:add")
    @Log(title = "项目超期", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WpmProPlanOverdueBo bo) {
        return toAjax(wpmProPlanOverdueService.insertByBo(bo));
    }

    /**
     * 修改项目超期
     */
    @SaCheckPermission("business:proPlanOverdue:edit")
    @Log(title = "项目超期", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WpmProPlanOverdueBo bo) {
        return toAjax(wpmProPlanOverdueService.updateByBo(bo));
    }

    /**
     * 删除项目超期
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:proPlanOverdue:remove")
    @Log(title = "项目超期", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(wpmProPlanOverdueService.deleteWithValidByIds(List.of(ids), true));
    }
    @GetMapping("/flow/{id}")
    public R<PlanOverdueFlowVo> getFlow(@NotNull(message = "主键不能为空") @PathVariable Long id){
        return R.ok(wpmProPlanOverdueService.getFlow(id));
    }

    @PutMapping("/saveReason")
    public R<Void> updateFlow(@RequestBody WpmProPlanOverdueBo bo){
        return toAjax(wpmProPlanOverdueService.updateFlow(bo));
    }

}
