package com.cyth;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

/**
 * 启动程序
 *
 * <AUTHOR>
 */

@SpringBootApplication(scanBasePackages = {"com.cyth.*"})
@EnableScheduling
public class WpmApplication {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(WpmApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  wpm-platform  启动成功   ლ(´ڡ`ლ)ﾞ");
    }

}
